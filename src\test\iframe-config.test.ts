/**
 * Test file for iFrame configuration functionality
 * Tests platform-specific iFrame attributes and sandbox permissions
 */

import { 
  getIFrameConfig, 
  getIFrameConfigDescription, 
  detectVideoPlatform 
} from '../utils/videoSecurity';

// Test the user's specific failing links
const userFailingLinks = [
  'https://gradehgplus.com/e/xvay1ggua7s7',
  'https://streamtape.com/v/YeRw6amy3MsvWa7/Dont.Leave.2022.720p.WEB-DL.English.ESubs.MoviesMod.com.mkv',
  'https://filemoon.to/e/ezfjmgsjwwsh'
];

// Additional test links for various platforms
const platformTestLinks = [
  { url: 'https://www.youtube.com/embed/dQw4w9WgXcQ', platform: 'youtube' },
  { url: 'https://player.vimeo.com/video/123456789', platform: 'vimeo' },
  { url: 'https://www.2embed.cc/embed/574475', platform: '2embed' },
  { url: 'https://example.com/embed/unknown', platform: 'unknown' }
];

/**
 * Test iFrame configuration for user's failing links
 */
export function testUserFailingLinksIFrameConfig(): boolean {
  console.group('🔧 Testing iFrame Config for User Failing Links');
  
  let allPassed = true;
  
  userFailingLinks.forEach((link, index) => {
    console.group(`Testing Link ${index + 1}: ${link.substring(0, 50)}...`);
    
    const platform = detectVideoPlatform(link);
    const config = getIFrameConfig(link);
    const description = getIFrameConfigDescription(config);
    
    console.log(`Platform: ${platform}`);
    console.log(`Sandbox: ${config.sandbox}`);
    console.log(`Referrer Policy: ${config.referrerPolicy}`);
    console.log(`Allow: ${config.allow}`);
    console.log(`Description: ${description}`);
    
    // Validate specific requirements for each platform
    let configValid = true;
    
    switch (platform) {
      case 'gradehgplus':
        // Should have allow-forms and allow-top-navigation-by-user-activation for gradehgplus
        if (!config.sandbox.includes('allow-forms') || 
            !config.sandbox.includes('allow-top-navigation-by-user-activation')) {
          console.error('❌ gradehgplus.com missing required sandbox permissions');
          configValid = false;
        }
        if (config.referrerPolicy !== 'strict-origin-when-cross-origin') {
          console.error('❌ gradehgplus.com should use strict-origin-when-cross-origin referrer policy');
          configValid = false;
        }
        break;
        
      case 'streamtape':
        // Should have allow-popups and allow-forms for streamtape
        if (!config.sandbox.includes('allow-popups') || 
            !config.sandbox.includes('allow-forms')) {
          console.error('❌ streamtape.com missing required sandbox permissions');
          configValid = false;
        }
        break;
        
      case 'filemoon':
        // Should have allow-popups and no-referrer policy for filemoon
        if (!config.sandbox.includes('allow-popups')) {
          console.error('❌ filemoon.to missing required sandbox permissions');
          configValid = false;
        }
        if (config.referrerPolicy !== 'no-referrer') {
          console.error('❌ filemoon.to should use no-referrer policy');
          configValid = false;
        }
        break;
    }
    
    if (configValid) {
      console.log(`✅ PASS - iFrame config is appropriate for ${platform}`);
    } else {
      console.error(`❌ FAIL - iFrame config issues for ${platform}`);
      allPassed = false;
    }
    
    console.groupEnd();
  });
  
  console.log(`\n📊 Result: ${allPassed ? 'ALL CONFIGS VALID' : 'SOME CONFIGS INVALID'}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Test iFrame configuration for various platforms
 */
export function testPlatformIFrameConfigs(): boolean {
  console.group('🎯 Testing Platform-Specific iFrame Configs');
  
  let allPassed = true;
  
  platformTestLinks.forEach(({ url, platform }) => {
    console.group(`Testing ${platform}: ${url.substring(0, 50)}...`);
    
    const detectedPlatform = detectVideoPlatform(url);
    const config = getIFrameConfig(url);
    const description = getIFrameConfigDescription(config);
    
    console.log(`Expected Platform: ${platform}`);
    console.log(`Detected Platform: ${detectedPlatform}`);
    console.log(`Config: ${description}`);
    
    // Verify platform detection matches expected
    if (detectedPlatform !== platform) {
      console.error(`❌ Platform detection mismatch: expected ${platform}, got ${detectedPlatform}`);
      allPassed = false;
    }
    
    // Verify config has required base permissions
    if (!config.sandbox.includes('allow-scripts') || 
        !config.sandbox.includes('allow-same-origin')) {
      console.error('❌ Missing required base sandbox permissions');
      allPassed = false;
    }
    
    // Verify config has valid referrer policy
    const validReferrerPolicies = [
      'no-referrer',
      'no-referrer-when-downgrade', 
      'strict-origin-when-cross-origin'
    ];
    if (!validReferrerPolicies.includes(config.referrerPolicy)) {
      console.error(`❌ Invalid referrer policy: ${config.referrerPolicy}`);
      allPassed = false;
    }
    
    console.log(`✅ Platform config validated`);
    console.groupEnd();
  });
  
  console.log(`\n📊 Platform Configs: ${allPassed ? 'ALL VALID' : 'SOME INVALID'}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Test security baseline maintenance
 */
export function testSecurityBaseline(): boolean {
  console.group('🛡️ Testing Security Baseline Maintenance');
  
  const testUrls = [...userFailingLinks, ...platformTestLinks.map(t => t.url)];
  let allSecure = true;
  
  testUrls.forEach((url, index) => {
    const config = getIFrameConfig(url);
    const platform = detectVideoPlatform(url);
    
    // All configs should have basic security permissions
    const requiredPermissions = ['allow-scripts', 'allow-same-origin'];
    const hasRequired = requiredPermissions.every(perm => config.sandbox.includes(perm));
    
    // Should not have dangerous permissions
    const dangerousPermissions = ['allow-top-navigation', 'allow-modals'];
    const hasDangerous = dangerousPermissions.some(perm => config.sandbox.includes(perm));
    
    console.log(`${index + 1}. ${platform}: ${hasRequired && !hasDangerous ? '✅' : '❌'} Security check`);
    
    if (!hasRequired) {
      console.error(`  Missing required permissions: ${requiredPermissions.join(', ')}`);
      allSecure = false;
    }
    
    if (hasDangerous) {
      console.error(`  Has dangerous permissions: ${dangerousPermissions.filter(p => config.sandbox.includes(p)).join(', ')}`);
      allSecure = false;
    }
  });
  
  console.log(`\n🛡️ Security Baseline: ${allSecure ? 'MAINTAINED' : 'COMPROMISED'}`);
  console.groupEnd();
  
  return allSecure;
}

/**
 * Run all iFrame configuration tests
 */
export function runIFrameConfigTests(): boolean {
  console.group('🧪 Running All iFrame Configuration Tests');
  
  const results = [
    testUserFailingLinksIFrameConfig(),
    testPlatformIFrameConfigs(),
    testSecurityBaseline()
  ];
  
  const allPassed = results.every(Boolean);
  const passedCount = results.filter(Boolean).length;
  
  console.log(`\n🎯 Overall Result: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  console.log(`📊 Summary: ${passedCount}/${results.length} test suites passed`);
  
  if (allPassed) {
    console.log('🎉 iFrame configurations should resolve the embedding issues!');
    console.log('✅ Platform-specific configs implemented correctly');
    console.log('✅ Security baseline maintained');
  } else {
    console.error('⚠️ Some configuration issues remain - check the failed tests above');
  }
  
  console.groupEnd();
  
  return allPassed;
}

// Export test data for use in other components
export { userFailingLinks, platformTestLinks };
