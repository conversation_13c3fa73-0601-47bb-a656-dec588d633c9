const express = require('express');
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator, logAdminAction } = require('../middleware/auth');

const router = express.Router();

// Validation rules for category creation/update
const categoryValidation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Name is required and must be less than 100 characters'),
  body('type')
    .isIn(['movie', 'series', 'both'])
    .withMessage('Type must be movie, series, or both'),
  body('slug')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Slug must contain only lowercase letters, numbers, and hyphens'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Active status must be boolean')
];

// Get all categories
router.get('/', async (req, res) => {
  try {
    const { active_only = 'true', type } = req.query;
    
    let query = 'SELECT * FROM categories';
    const params = [];
    const conditions = [];

    if (active_only === 'true') {
      conditions.push('is_active = 1');
    }

    if (type && ['movie', 'series', 'both'].includes(type)) {
      conditions.push('(type = ? OR type = "both")');
      params.push(type);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY name';

    const categories = await db.execute(query, params);

    res.json({
      success: true,
      data: categories
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch categories'
    });
  }
});

// Get single category
router.get('/:id', async (req, res) => {
  try {
    const categoryId = req.params.id;
    const [category] = await db.execute('SELECT * FROM categories WHERE id = ?', [categoryId]);

    if (!category) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Category not found'
      });
    }

    // Get content count for this category
    const [contentCount] = await db.execute(
      'SELECT COUNT(*) as count FROM content WHERE category_id = ?',
      [categoryId]
    );

    res.json({
      success: true,
      data: {
        ...category,
        content_count: contentCount.count
      }
    });

  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch category'
    });
  }
});

// Create new category (admin/moderator only)
router.post('/', authenticateToken, requireModerator, categoryValidation, logAdminAction('category_create'), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { name, type, description, is_active = true } = req.body;
    
    // Generate slug if not provided
    let { slug } = req.body;
    if (!slug) {
      slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    }

    // Check if name or slug already exists
    const [existing] = await db.execute(
      'SELECT id FROM categories WHERE name = ? OR slug = ?',
      [name, slug]
    );

    if (existing) {
      return res.status(409).json({
        error: 'Conflict',
        message: 'Category name or slug already exists'
      });
    }

    // Insert new category
    const result = await db.execute(
      'INSERT INTO categories (name, type, slug, description, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
      [name, type, slug, description, is_active]
    );

    const categoryId = result.insertId;
    const [newCategory] = await db.execute('SELECT * FROM categories WHERE id = ?', [categoryId]);

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: newCategory
    });

  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create category'
    });
  }
});

// Update category (admin/moderator only)
router.put('/:id', authenticateToken, requireModerator, categoryValidation, logAdminAction('category_update'), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const categoryId = req.params.id;
    const { name, type, description, is_active } = req.body;
    
    // Generate slug if not provided
    let { slug } = req.body;
    if (!slug) {
      slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    }

    // Check if category exists
    const [existingCategory] = await db.execute('SELECT * FROM categories WHERE id = ?', [categoryId]);
    if (!existingCategory) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Category not found'
      });
    }

    // Check if name or slug conflicts with other categories
    const [conflicting] = await db.execute(
      'SELECT id FROM categories WHERE (name = ? OR slug = ?) AND id != ?',
      [name, slug, categoryId]
    );

    if (conflicting) {
      return res.status(409).json({
        error: 'Conflict',
        message: 'Category name or slug already exists'
      });
    }

    // Update category
    await db.execute(
      'UPDATE categories SET name = ?, type = ?, slug = ?, description = ?, is_active = ?, updated_at = NOW() WHERE id = ?',
      [name, type, slug, description, is_active, categoryId]
    );

    const [updatedCategory] = await db.execute('SELECT * FROM categories WHERE id = ?', [categoryId]);

    res.json({
      success: true,
      message: 'Category updated successfully',
      data: updatedCategory
    });

  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update category'
    });
  }
});

// Delete category (admin/moderator only)
router.delete('/:id', authenticateToken, requireModerator, logAdminAction('category_delete'), async (req, res) => {
  try {
    const categoryId = req.params.id;

    // Check if category exists
    const [existingCategory] = await db.execute('SELECT * FROM categories WHERE id = ?', [categoryId]);
    if (!existingCategory) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Category not found'
      });
    }

    // Check if category has content
    const [contentCount] = await db.execute(
      'SELECT COUNT(*) as count FROM content WHERE category_id = ?',
      [categoryId]
    );

    if (contentCount.count > 0) {
      return res.status(409).json({
        error: 'Conflict',
        message: `Cannot delete category. It has ${contentCount.count} content items. Please move or delete the content first.`
      });
    }

    // Delete category
    await db.execute('DELETE FROM categories WHERE id = ?', [categoryId]);

    res.json({
      success: true,
      message: 'Category deleted successfully',
      data: {
        id: categoryId,
        name: existingCategory.name
      }
    });

  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete category'
    });
  }
});

// Get content by category
router.get('/:slug/content', async (req, res) => {
  try {
    const categorySlug = req.params.slug;
    const { page = 1, limit = 20, published_only = 'true' } = req.query;
    
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get category
    const [category] = await db.execute('SELECT * FROM categories WHERE slug = ?', [categorySlug]);
    if (!category) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Category not found'
      });
    }

    // Build query
    let query = `
      SELECT c.*, cat.name as category_name, cat.slug as category_slug
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.category_id = ?
    `;
    const params = [category.id];

    if (published_only === 'true') {
      query += ' AND c.is_published = 1';
    }

    query += ' ORDER BY c.created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);

    const content = await db.execute(query, params);

    // Get total count
    let countQuery = 'SELECT COUNT(*) as count FROM content WHERE category_id = ?';
    const countParams = [category.id];
    
    if (published_only === 'true') {
      countQuery += ' AND is_published = 1';
    }

    const [totalCount] = await db.execute(countQuery, countParams);
    const totalPages = Math.ceil(totalCount.count / parseInt(limit));

    res.json({
      success: true,
      data: {
        category,
        content,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          totalCount: totalCount.count,
          totalPages,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Error fetching category content:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch category content'
    });
  }
});

module.exports = router;
