<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StreamDB Test Page</title>
    <style>
        body {
            background: #0a0a0a;
            color: #e6cb8e;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
        }
        h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }
        p {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .status {
            background: #1a1a1a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            color: #22c55e;
        }
        .error {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>StreamDB Test Page</h1>
        <p>This is a simple test page to verify that the deployment is working correctly.</p>
        
        <div class="status">
            <h3>Deployment Status</h3>
            <p class="success">✅ Static files are loading correctly</p>
            <p class="success">✅ HTML is rendering properly</p>
            <p class="success">✅ CSS styles are applied</p>
        </div>
        
        <p>If you can see this page, the basic deployment is working. The issue with the main application might be related to JavaScript loading or React Router configuration.</p>
        
        <div style="margin-top: 40px;">
            <a href="/index.html" style="color: #e6cb8e; text-decoration: underline;">
                Try loading the main application
            </a>
        </div>
    </div>
    
    <script>
        console.log("Test page loaded successfully");
        console.log("Current URL:", window.location.href);
        console.log("User Agent:", navigator.userAgent);
    </script>
</body>
</html>
