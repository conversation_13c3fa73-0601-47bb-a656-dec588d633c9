# Database Setup Guide for StreamDB Authentication

## Overview

This guide provides comprehensive instructions for setting up secure database-based authentication for your StreamDB Admin Panel. The current implementation uses client-side demo credentials for development, but this guide will help you transition to a secure server-side authentication system.

## Database Schema

### 1. Admin Users Table

Create a table to store admin user credentials:

```sql
-- MySQL/MariaDB
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    role ENUM('admin', 'moderator') DEFAULT 'admin',
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);

-- PostgreSQL
CREATE TABLE admin_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'moderator')),
    permissions JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_admin_username ON admin_users(username);
CREATE INDEX idx_admin_email ON admin_users(email);
CREATE INDEX idx_admin_active ON admin_users(is_active);
```

### 2. Session Management Table

```sql
-- MySQL/MariaDB
CREATE TABLE admin_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- PostgreSQL
CREATE TABLE admin_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES admin_users(id) ON DELETE CASCADE,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_session_user_id ON admin_sessions(user_id);
CREATE INDEX idx_session_expires_at ON admin_sessions(expires_at);
```

### 3. Security Audit Log Table

```sql
-- MySQL/MariaDB
CREATE TABLE admin_security_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    event_type VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details JSON,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_event_type (event_type),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at)
);

-- PostgreSQL
CREATE TABLE admin_security_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES admin_users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    details JSONB,
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_log_event_type ON admin_security_logs(event_type);
CREATE INDEX idx_log_severity ON admin_security_logs(severity);
CREATE INDEX idx_log_created_at ON admin_security_logs(created_at);
```

## Creating Admin Users

### 1. Using bcrypt for Password Hashing

**Node.js Example:**
```javascript
const bcrypt = require('bcrypt');

async function createAdminUser(username, password, email, role = 'admin') {
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    const permissions = [
        'admin_panel_access',
        'content_create',
        'content_edit',
        'content_delete',
        'bulk_operations',
        'export_data'
    ];
    
    const query = `
        INSERT INTO admin_users (username, password_hash, email, role, permissions)
        VALUES (?, ?, ?, ?, ?)
    `;
    
    // Execute query with your database connection
    await db.execute(query, [username, passwordHash, email, role, JSON.stringify(permissions)]);
}

// Create your admin user
createAdminUser('admin', 'your_secure_password_here', '<EMAIL>', 'admin');
```

**Python Example:**
```python
import bcrypt
import json

def create_admin_user(username, password, email, role='admin'):
    # Hash password
    password_bytes = password.encode('utf-8')
    salt = bcrypt.gensalt(rounds=12)
    password_hash = bcrypt.hashpw(password_bytes, salt).decode('utf-8')
    
    permissions = [
        'admin_panel_access',
        'content_create',
        'content_edit',
        'content_delete',
        'bulk_operations',
        'export_data'
    ]
    
    query = """
        INSERT INTO admin_users (username, password_hash, email, role, permissions)
        VALUES (%s, %s, %s, %s, %s)
    """
    
    # Execute with your database connection
    cursor.execute(query, (username, password_hash, email, role, json.dumps(permissions)))
    connection.commit()

# Create your admin user
create_admin_user('admin', 'your_secure_password_here', '<EMAIL>', 'admin')
```

### 2. Direct SQL Insert (Less Secure)

```sql
-- Generate a bcrypt hash using an online tool or command line
-- bcrypt hash for 'your_secure_password_here' with cost 12:
-- $2b$12$example_hash_here

INSERT INTO admin_users (username, password_hash, email, role, permissions) VALUES (
    'admin',
    '$2b$12$your_bcrypt_hash_here',
    '<EMAIL>',
    'admin',
    '["admin_panel_access", "content_create", "content_edit", "content_delete", "bulk_operations", "export_data"]'
);
```

## Security Best Practices

### 1. Password Requirements

- Minimum 12 characters
- Mix of uppercase, lowercase, numbers, and symbols
- Not based on dictionary words
- Unique for each admin user
- Regularly rotated (every 90 days)

### 2. Database Security

```sql
-- Create dedicated database user for the application
CREATE USER 'streamdb_app'@'localhost' IDENTIFIED BY 'secure_random_password';

-- Grant only necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb.admin_users TO 'streamdb_app'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb.admin_sessions TO 'streamdb_app'@'localhost';
GRANT INSERT ON streamdb.admin_security_logs TO 'streamdb_app'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;
```

### 3. Environment Variables

Create a `.env` file (never commit to version control):

```env
# Database Configuration
DATABASE_URL=mysql://streamdb_app:secure_password@localhost:3306/streamdb
# or for PostgreSQL:
# DATABASE_URL=postgresql://streamdb_app:secure_password@localhost:5432/streamdb

# JWT Configuration
JWT_SECRET=your_very_long_random_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# Session Configuration
SESSION_SECRET=your_very_long_random_session_secret_key_here
SESSION_TIMEOUT=86400000

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# CORS Configuration
CORS_ORIGIN=https://yourdomain.com
```

## Server-Side Implementation

### 1. Authentication Middleware (Express.js)

```javascript
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        // Get user from database
        const user = await db.query(
            'SELECT * FROM admin_users WHERE username = ? AND is_active = TRUE',
            [username]
        );
        
        if (!user || user.length === 0) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        const userData = user[0];
        
        // Check if account is locked
        if (userData.locked_until && new Date() < userData.locked_until) {
            return res.status(423).json({ error: 'Account locked' });
        }
        
        // Verify password
        const isValidPassword = await bcrypt.compare(password, userData.password_hash);
        
        if (!isValidPassword) {
            // Increment failed attempts
            await db.query(
                'UPDATE admin_users SET failed_login_attempts = failed_login_attempts + 1 WHERE id = ?',
                [userData.id]
            );
            
            // Lock account if too many attempts
            if (userData.failed_login_attempts >= 4) {
                const lockUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
                await db.query(
                    'UPDATE admin_users SET locked_until = ? WHERE id = ?',
                    [lockUntil, userData.id]
                );
            }
            
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        // Reset failed attempts on successful login
        await db.query(
            'UPDATE admin_users SET failed_login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE id = ?',
            [userData.id]
        );
        
        // Generate JWT token
        const token = jwt.sign(
            { 
                userId: userData.id, 
                username: userData.username, 
                role: userData.role 
            },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN }
        );
        
        // Create session
        const sessionId = require('crypto').randomBytes(64).toString('hex');
        await db.query(
            'INSERT INTO admin_sessions (id, user_id, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, ?)',
            [sessionId, userData.id, req.ip, req.get('User-Agent'), new Date(Date.now() + 24 * 60 * 60 * 1000)]
        );
        
        res.json({
            success: true,
            token,
            user: {
                id: userData.id,
                username: userData.username,
                role: userData.role,
                permissions: JSON.parse(userData.permissions || '[]')
            }
        });
        
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
```

### 2. Authentication Verification Middleware

```javascript
const verifyAuth = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Check if user still exists and is active
        const user = await db.query(
            'SELECT * FROM admin_users WHERE id = ? AND is_active = TRUE',
            [decoded.userId]
        );
        
        if (!user || user.length === 0) {
            return res.status(401).json({ error: 'Invalid token' });
        }
        
        req.user = user[0];
        next();
        
    } catch (error) {
        return res.status(401).json({ error: 'Invalid token' });
    }
};

// Protect admin routes
app.use('/api/admin/*', verifyAuth);
```

## Migration from Demo Credentials

### 1. Update Frontend Configuration

Update your `.env` file:
```env
VITE_ENABLE_DEMO_CREDENTIALS=false
VITE_API_BASE_URL=https://api.yourdomain.com
```

### 2. Update AuthContext

Replace the demo authentication logic with API calls:

```typescript
const login = async (credentials: LoginCredentials): Promise<LoginResponse> => {
    try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials),
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            return { success: false, error: data.error };
        }
        
        return { success: true, user: data.user, token: data.token };
        
    } catch (error) {
        return { success: false, error: 'Network error' };
    }
};
```

## Monitoring and Maintenance

### 1. Regular Security Audits

```sql
-- Check for suspicious login patterns
SELECT 
    username,
    COUNT(*) as failed_attempts,
    MAX(created_at) as last_attempt
FROM admin_security_logs 
WHERE event_type = 'LOGIN_FAILED' 
    AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY username
HAVING failed_attempts > 10;

-- Check active sessions
SELECT 
    s.id,
    u.username,
    s.ip_address,
    s.created_at,
    s.last_activity
FROM admin_sessions s
JOIN admin_users u ON s.user_id = u.id
WHERE s.expires_at > NOW()
ORDER BY s.last_activity DESC;
```

### 2. Cleanup Tasks

```sql
-- Clean up expired sessions (run daily)
DELETE FROM admin_sessions WHERE expires_at < NOW();

-- Clean up old security logs (run weekly)
DELETE FROM admin_security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

## Troubleshooting

### Common Issues

1. **"Invalid credentials" error**: Check password hash generation
2. **"Account locked" error**: Reset `locked_until` field in database
3. **Session expires immediately**: Check server time synchronization
4. **CORS errors**: Update `CORS_ORIGIN` environment variable

### Reset Admin Password

```sql
-- Generate new bcrypt hash and update
UPDATE admin_users 
SET password_hash = '$2b$12$new_bcrypt_hash_here',
    failed_login_attempts = 0,
    locked_until = NULL
WHERE username = 'admin';
```

This guide provides a comprehensive foundation for implementing secure database-based authentication. Always test thoroughly in a development environment before deploying to production.
