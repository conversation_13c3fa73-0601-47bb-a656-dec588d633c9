/**
 * Debug script for 2embed.cc validation issue
 * Tests the specific link format that's failing validation
 */

import { isValidVideoLink, isSecureVideoLink, parseVideoLinks } from '../utils/videoSecurity';

// The problematic link from user
const problematicLink = 'https://www.2embed.cc/movie/574475';

// Research: 2embed.cc typical embed formats
const possibleFormats = [
  // User's current format
  'https://www.2embed.cc/movie/574475',
  
  // Possible correct embed formats for 2embed.cc
  'https://www.2embed.cc/embed/movie/574475',
  'https://www.2embed.cc/embed/574475',
  'https://2embed.cc/embed/movie/574475',
  'https://2embed.cc/embed/574475',
  
  // Iframe formats
  '<iframe src="https://www.2embed.cc/embed/movie/574475" allowfullscreen></iframe>',
  '<iframe src="https://2embed.cc/embed/574475" allowfullscreen></iframe>',
  
  // Protocol-relative
  '//www.2embed.cc/embed/movie/574475',
  '//2embed.cc/embed/574475',
];

/**
 * Debug the validation failure for 2embed.cc
 */
export function debug2EmbedValidation(): void {
  console.group('🔍 2embed.cc Validation Debug');
  
  console.log('Testing problematic link:', problematicLink);
  
  // Test the user's link
  const isValid = isValidVideoLink(problematicLink);
  const isSecure = isSecureVideoLink(problematicLink);
  
  console.log('Results for user link:');
  console.log('  isValidVideoLink:', isValid);
  console.log('  isSecureVideoLink:', isSecure);
  
  if (!isValid) {
    console.log('❌ Link failed validation - this explains the error message');
  }
  
  // Test all possible formats
  console.log('\n🧪 Testing possible correct formats:');
  possibleFormats.forEach((format, index) => {
    const valid = isValidVideoLink(format);
    const secure = isSecureVideoLink(format);
    console.log(`${index + 1}. ${valid ? '✅' : '❌'} ${format}`);
    if (!valid) {
      console.log(`   Validation: ${valid}, Security: ${secure}`);
    }
  });
  
  console.groupEnd();
}

/**
 * Test current validation patterns against 2embed.cc domain
 */
export function test2EmbedPatterns(): void {
  console.group('🎯 2embed.cc Pattern Analysis');
  
  // Get current patterns from the validation function
  const testPatterns = [
    { name: 'Generic embed', pattern: /^https?:\/\/.*\/embed\//i },
    { name: 'Generic player', pattern: /^https?:\/\/.*\/player\//i },
    { name: 'Generic embed path', pattern: /\/embed\/[a-zA-Z0-9_-]+/i },
    { name: 'Generic player path', pattern: /\/player\/[a-zA-Z0-9_-]+/i },
    { name: 'Protocol-relative embed', pattern: /^\/\/[^\/]+\/(embed|player)\//i },
  ];
  
  console.log('Testing user link against individual patterns:');
  testPatterns.forEach(({ name, pattern }) => {
    const matches = pattern.test(problematicLink);
    console.log(`  ${matches ? '✅' : '❌'} ${name}: ${pattern}`);
  });
  
  // Test what pattern would work for 2embed.cc
  console.log('\n💡 Patterns that would work for 2embed.cc:');
  const workingPatterns = [
    { name: '2embed.cc movie pattern', pattern: /2embed\.cc\/movie\//i },
    { name: '2embed.cc generic', pattern: /2embed\.cc\//i },
    { name: 'Movie path pattern', pattern: /\/movie\/[a-zA-Z0-9_-]+/i },
  ];
  
  workingPatterns.forEach(({ name, pattern }) => {
    const matches = pattern.test(problematicLink);
    console.log(`  ${matches ? '✅' : '❌'} ${name}: ${pattern}`);
  });
  
  console.groupEnd();
}

/**
 * Research 2embed.cc correct embed format
 */
export function research2EmbedFormat(): void {
  console.group('📚 2embed.cc Format Research');
  
  console.log('2embed.cc is a popular video hosting/embedding service.');
  console.log('Common patterns for 2embed.cc:');
  console.log('');
  console.log('❌ Direct movie page: https://www.2embed.cc/movie/574475');
  console.log('   This is typically a landing page, not an embed URL');
  console.log('');
  console.log('✅ Correct embed formats:');
  console.log('   https://www.2embed.cc/embed/574475');
  console.log('   https://2embed.cc/embed/574475');
  console.log('   <iframe src="https://www.2embed.cc/embed/574475"></iframe>');
  console.log('');
  console.log('🔍 The user likely needs to:');
  console.log('   1. Use the embed URL instead of the movie page URL');
  console.log('   2. Or we need to add support for 2embed.cc movie URLs');
  
  console.groupEnd();
}

/**
 * Run all debug functions
 */
export function runAll2EmbedDebug(): void {
  debug2EmbedValidation();
  test2EmbedPatterns();
  research2EmbedFormat();
  
  console.log('\n🎯 SUMMARY:');
  console.log('The validation error occurs because:');
  console.log('1. User is using movie page URL, not embed URL');
  console.log('2. Current patterns expect /embed/ or /player/ paths');
  console.log('3. 2embed.cc uses /movie/ for landing pages');
  console.log('');
  console.log('💡 SOLUTIONS:');
  console.log('1. Add 2embed.cc specific patterns to validation');
  console.log('2. Guide user to use correct embed format');
  console.log('3. Add URL transformation for 2embed.cc');
}

// Export for console testing
(window as any).debug2Embed = {
  debug2EmbedValidation,
  test2EmbedPatterns,
  research2EmbedFormat,
  runAll2EmbedDebug
};
