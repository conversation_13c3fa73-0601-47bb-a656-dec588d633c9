/**
 * Comprehensive Ad Blocker Detection System
 * 
 * This module provides utilities for detecting various types of ad blockers
 * including browser extensions, antivirus software blockers, and ad-blocking browsers.
 */

export interface AdBlockerInfo {
  name: string;
  type: 'extension' | 'browser' | 'antivirus' | 'unknown';
  detected: boolean;
  confidence: number; // 0-100
  detectionMethod: string;
  disableInstructions: string[];
  whitelistInstructions: string[];
}

export interface AdBlockerDetectionResult {
  hasAdBlocker: boolean;
  detectedBlockers: AdBlockerInfo[];
  totalConfidence: number;
  detectionTimestamp: number;
}

// Known ad blocker signatures and detection patterns
const AD_BLOCKER_SIGNATURES = {
  // Browser Extensions
  uBlockOrigin: {
    name: 'uBlock Origin',
    type: 'extension' as const,
    detectionMethods: [
      'elementHiding',
      'scriptBlocking',
      'networkBlocking',
      'domModification'
    ],
    disableInstructions: [
      'Click the uBlock Origin icon in your browser toolbar',
      'Click the large power button to disable uBlock Origin',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the uBlock Origin icon in your browser toolbar',
      'Click "No" under "Disable uBlock Origin for this site"',
      'Or click the "Trust this site" button',
      'Refresh the page to continue watching'
    ]
  },
  adBlockPlus: {
    name: 'Adblock Plus',
    type: 'extension' as const,
    detectionMethods: [
      'elementHiding',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the Adblock Plus icon in your browser toolbar',
      'Click "Enabled on this site" to disable it',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the Adblock Plus icon in your browser toolbar',
      'Click "Enabled on this site" to turn it off',
      'Or go to Settings > Whitelisted websites and add this domain',
      'Refresh the page to continue watching'
    ]
  },
  adBlock: {
    name: 'AdBlock',
    type: 'extension' as const,
    detectionMethods: [
      'elementHiding',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the AdBlock icon in your browser toolbar',
      'Click "Pause AdBlock" or "Don\'t run on this site"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the AdBlock icon in your browser toolbar',
      'Click "Don\'t run on pages on this domain"',
      'Refresh the page to continue watching'
    ]
  },
  ghostery: {
    name: 'Ghostery',
    type: 'extension' as const,
    detectionMethods: [
      'scriptBlocking',
      'networkBlocking',
      'trackerBlocking'
    ],
    disableInstructions: [
      'Click the Ghostery icon in your browser toolbar',
      'Toggle off "Ad Blocking" or "Enhanced Ad Blocking"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the Ghostery icon in your browser toolbar',
      'Click "Trust Site" or add this domain to trusted sites',
      'Refresh the page to continue watching'
    ]
  },
  adGuard: {
    name: 'AdGuard',
    type: 'extension' as const,
    detectionMethods: [
      'elementHiding',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the AdGuard icon in your browser toolbar',
      'Click "Pause protection on this website"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the AdGuard icon in your browser toolbar',
      'Click "Add to whitelist" or "Don\'t filter this website"',
      'Refresh the page to continue watching'
    ]
  },
  privacyBadger: {
    name: 'Privacy Badger',
    type: 'extension' as const,
    detectionMethods: [
      'scriptBlocking',
      'networkBlocking',
      'trackerBlocking'
    ],
    disableInstructions: [
      'Click the Privacy Badger icon in your browser toolbar',
      'Click "Disable Privacy Badger for this site"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the Privacy Badger icon in your browser toolbar',
      'Click "Disable Privacy Badger for this site"',
      'Refresh the page to continue watching'
    ]
  },
  operaAdBlocker: {
    name: 'Opera Ad Blocker',
    type: 'browser' as const,
    detectionMethods: [
      'userAgent',
      'elementHiding',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the Opera menu (three lines) in the top left',
      'Go to Settings > Basic > Block ads',
      'Toggle off "Block ads and surf the web up to three times faster"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the shield icon in the Opera address bar',
      'Click "Turn off ad blocker for this site"',
      'Refresh the page to continue watching'
    ]
  },
  // Browser Built-in Blockers
  braveShields: {
    name: 'Brave Shields',
    type: 'browser' as const,
    detectionMethods: [
      'userAgent',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the Brave Shields icon in the address bar',
      'Toggle "Shields" to "Down" for this site',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the Brave Shields icon in the address bar',
      'Toggle "Shields" to "Down" for this site',
      'Or adjust individual shield settings',
      'Refresh the page to continue watching'
    ]
  },
  firefoxTracking: {
    name: 'Firefox Enhanced Tracking Protection',
    type: 'browser' as const,
    detectionMethods: [
      'userAgent',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the shield icon in the Firefox address bar',
      'Click "Turn off Blocking for This Site"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the shield icon in the Firefox address bar',
      'Click "Turn off Blocking for This Site"',
      'Refresh the page to continue watching'
    ]
  },
  // Antivirus Software Blockers
  avastSecureLine: {
    name: 'Avast SecureLine/Web Shield',
    type: 'antivirus' as const,
    detectionMethods: [
      'networkBlocking',
      'scriptBlocking'
    ],
    disableInstructions: [
      'Open Avast Antivirus',
      'Go to Protection > Core Shields',
      'Temporarily disable "Web Shield"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Open Avast Antivirus',
      'Go to Protection > Core Shields > Web Shield Settings',
      'Add this website to exceptions',
      'Refresh the page to continue watching'
    ]
  },
  avgWebShield: {
    name: 'AVG Web Shield',
    type: 'antivirus' as const,
    detectionMethods: [
      'networkBlocking',
      'scriptBlocking'
    ],
    disableInstructions: [
      'Open AVG Antivirus',
      'Go to Computer > Web & Email Protection',
      'Temporarily disable "Web Shield"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Open AVG Antivirus',
      'Go to Computer > Web & Email Protection > Web Shield',
      'Add this website to exceptions',
      'Refresh the page to continue watching'
    ]
  },
  kasperskyWebAntiVirus: {
    name: 'Kaspersky Web Anti-Virus',
    type: 'antivirus' as const,
    detectionMethods: [
      'networkBlocking',
      'scriptBlocking'
    ],
    disableInstructions: [
      'Open Kaspersky Security Center',
      'Go to Protection > Web Anti-Virus',
      'Temporarily disable protection',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Open Kaspersky Security Center',
      'Go to Protection > Web Anti-Virus > Settings',
      'Add this website to trusted sites',
      'Refresh the page to continue watching'
    ]
  }
};

/**
 * Test for element hiding detection
 * Creates test elements that ad blockers typically hide
 */
function testElementHiding(): Promise<boolean> {
  return new Promise((resolve) => {
    const isProduction = import.meta.env.PROD;
    const debugPrefix = isProduction ? '🔍 [PROD]' : '🔍 [DEV]';

    console.log(`${debugPrefix} Starting element hiding test...`);

    const testElements = [
      // Common ad class names
      'ads', 'ad', 'advertisement', 'advert', 'ad-banner', 'ad-container',
      'ad-slot', 'ad-space', 'ad-unit', 'ad-wrapper', 'ad-block', 'ad-box',
      'ad-frame', 'ad-content', 'ad-section', 'ad-area', 'ad-zone',

      // Google Ads specific
      'google-ads', 'googleads', 'googlesyndication', 'adsbygoogle', 'gpt-ad',
      'dfp-ad', 'doubleclick', 'adsense', 'google-ad-unit',

      // Other ad networks
      'amazon-ads', 'facebook-ads', 'twitter-ads', 'linkedin-ads',
      'outbrain', 'taboola', 'revcontent', 'content-ads',

      // Sponsored content
      'sponsored', 'sponsored-content', 'sponsored-post', 'promoted',
      'promoted-content', 'native-ad', 'native-ads',

      // Banner and display ads
      'banner', 'banner-ad', 'display-ad', 'leaderboard', 'skyscraper',
      'rectangle', 'square-ad', 'popup-ad', 'overlay-ad',

      // Video ads
      'video-ad', 'preroll', 'midroll', 'postroll', 'video-ads',

      // Mobile ads
      'mobile-ad', 'mobile-ads', 'interstitial', 'rewarded-ad',

      // Tracking and analytics (often blocked)
      'tracking', 'analytics', 'pixel', 'beacon', 'tracker'
    ];

    let hiddenCount = 0;
    const totalElements = testElements.length;
    let completedTests = 0;

    console.log(`${debugPrefix} Testing ${totalElements} elements for hiding...`);

    testElements.forEach((className, index) => {
      console.log(`${debugPrefix} Creating test element ${index + 1}: ${className}`);

      const testDiv = document.createElement('div');
      testDiv.className = className;
      // Position element in visible area but make it tiny
      testDiv.style.position = 'absolute';
      testDiv.style.top = '0px';
      testDiv.style.left = '0px';
      testDiv.style.width = '1px';
      testDiv.style.height = '1px';
      testDiv.style.overflow = 'hidden';
      testDiv.style.zIndex = '-1000';
      testDiv.innerHTML = '&nbsp;';

      try {
        document.body.appendChild(testDiv);
      } catch (error) {
        console.error(`${debugPrefix} Failed to append test element ${index + 1}:`, error);
        completedTests++;
        if (completedTests === totalElements) {
          const result = hiddenCount / totalElements >= 0.7;
          console.log(`${debugPrefix} Element hiding test complete (with errors): ${hiddenCount}/${totalElements} hidden, result: ${result}`);
          resolve(result);
        }
        return;
      }

      // Check if element is hidden after a short delay
      setTimeout(() => {
        try {
          const computedStyle = window.getComputedStyle(testDiv);

          // More precise detection - element must be actively hidden by CSS rules
          const isActivelyHidden = (
            computedStyle.display === 'none' ||
            computedStyle.visibility === 'hidden' ||
            computedStyle.opacity === '0' ||
            (testDiv.offsetHeight === 0 && testDiv.offsetWidth === 0 && computedStyle.height !== '0px')
          );

          if (isActivelyHidden) {
            hiddenCount++;
            console.log(`${debugPrefix} Element ${index + 1} (${className}) is hidden`);
          } else {
            console.log(`${debugPrefix} Element ${index + 1} (${className}) is visible`);
          }

          document.body.removeChild(testDiv);
        } catch (error) {
          console.error(`${debugPrefix} Error checking element ${index + 1} (${className}):`, error);
          // If element was already removed or error occurred, don't count as hidden
          try {
            if (document.body.contains(testDiv)) {
              document.body.removeChild(testDiv);
            }
          } catch (e) {
            // Ignore cleanup errors
          }
        }

        completedTests++;
        if (completedTests === totalElements) {
          // Lower threshold for production - even 20% hidden elements indicates ad blocker
          const threshold = isProduction ? 0.2 : 0.7;
          const result = hiddenCount / totalElements >= threshold;
          console.log(`${debugPrefix} Element hiding test complete: ${hiddenCount}/${totalElements} hidden (${(hiddenCount/totalElements*100).toFixed(1)}%), threshold: ${threshold*100}%, result: ${result}`);
          resolve(result);
        }
      }, 150);
    });
  });
}

/**
 * Test for script blocking detection
 * Attempts to load known ad-serving scripts
 */
function testScriptBlocking(): Promise<boolean> {
  return new Promise((resolve) => {
    const isProduction = import.meta.env.PROD;
    const debugPrefix = isProduction ? '🔍 [PROD]' : '🔍 [DEV]';

    console.log(`${debugPrefix} Starting script blocking test...`);

    const testScripts = [
      'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js',
      'https://www.googletagservices.com/tag/js/gpt.js',
      'https://securepubads.g.doubleclick.net/tag/js/gpt.js'
    ];

    let blockedCount = 0;
    let completedTests = 0;
    // Shorter timeout for production to avoid Vercel function timeouts
    const timeoutDuration = isProduction ? 1500 : 2000;

    console.log(`${debugPrefix} Testing ${testScripts.length} scripts with ${timeoutDuration}ms timeout...`);

    testScripts.forEach((scriptUrl, index) => {
      console.log(`${debugPrefix} Testing script ${index + 1}: ${scriptUrl}`);

      const script = document.createElement('script');
      script.src = scriptUrl;
      script.async = true;

      const timeout = setTimeout(() => {
        console.log(`${debugPrefix} Script ${index + 1} timed out (likely blocked)`);
        blockedCount++;
        completedTests++;
        if (completedTests === testScripts.length) {
          console.log(`${debugPrefix} Script blocking test complete: ${blockedCount}/${testScripts.length} blocked`);
          resolve(blockedCount > 0);
        }
      }, timeoutDuration);

      script.onload = () => {
        console.log(`${debugPrefix} Script ${index + 1} loaded successfully`);
        clearTimeout(timeout);
        completedTests++;
        if (completedTests === testScripts.length) {
          console.log(`${debugPrefix} Script blocking test complete: ${blockedCount}/${testScripts.length} blocked`);
          resolve(blockedCount > 0);
        }
      };

      script.onerror = () => {
        console.log(`${debugPrefix} Script ${index + 1} failed to load (blocked or network error)`);
        clearTimeout(timeout);
        blockedCount++;
        completedTests++;
        if (completedTests === testScripts.length) {
          console.log(`${debugPrefix} Script blocking test complete: ${blockedCount}/${testScripts.length} blocked`);
          resolve(blockedCount > 0);
        }
      };

      try {
        document.head.appendChild(script);
      } catch (error) {
        console.error(`${debugPrefix} Failed to append script ${index + 1}:`, error);
        clearTimeout(timeout);
        blockedCount++;
        completedTests++;
        if (completedTests === testScripts.length) {
          console.log(`${debugPrefix} Script blocking test complete: ${blockedCount}/${testScripts.length} blocked`);
          resolve(blockedCount > 0);
        }
      }

      // Clean up with shorter timeout in production
      setTimeout(() => {
        try {
          if (script.parentNode) {
            script.parentNode.removeChild(script);
            console.log(`${debugPrefix} Cleaned up script ${index + 1}`);
          }
        } catch (error) {
          console.warn(`${debugPrefix} Failed to clean up script ${index + 1}:`, error);
        }
      }, timeoutDuration + 1000);
    });

    // Fallback timeout to prevent hanging
    setTimeout(() => {
      if (completedTests < testScripts.length) {
        console.warn(`${debugPrefix} Script blocking test fallback timeout triggered`);
        resolve(blockedCount > 0);
      }
    }, timeoutDuration + 2000);
  });
}

/**
 * Advanced ad blocker detection using multiple sophisticated methods
 * Designed to work reliably in production environments like Vercel
 */
export function testAdvancedAdBlockerDetection(): Promise<boolean> {
  return new Promise((resolve) => {
    const isProduction = import.meta.env.PROD;
    const debugPrefix = isProduction ? '🔍 [PROD]' : '🔍 [DEV]';

    console.log(`${debugPrefix} Starting advanced ad blocker detection...`);

    let detectionScore = 0;
    const maxScore = 15;
    const detectionResults: string[] = [];

    // Test 1: Check for common ad blocker global variables and properties
    try {
      const adBlockerGlobals = [
        'uBlock', 'adblock', 'AdBlock', 'adblockplus', 'AdblockPlus',
        'ghostery', 'Ghostery', 'adguard', 'AdGuard', 'adnauseam',
        'disconnect', 'privacy', 'blockAdBlock', 'canRunAds', 'isAdBlockActive'
      ];

      for (const global of adBlockerGlobals) {
        if (typeof (window as any)[global] !== 'undefined') {
          detectionScore++;
          detectionResults.push(`Global variable: ${global}`);
          console.log(`${debugPrefix} Found ad blocker global: ${global}`);
        }
      }
    } catch (e) {
      console.log(`${debugPrefix} Error checking globals:`, e);
    }

    // Test 2: Check for modified browser APIs
    try {
      // Check if fetch has been modified
      const fetchStr = window.fetch.toString();
      if (fetchStr.length < 50 || fetchStr.includes('native code') === false) {
        detectionScore++;
        detectionResults.push('Modified fetch API');
        console.log(`${debugPrefix} Detected modified fetch API`);
      }

      // Check XMLHttpRequest modifications
      const xhrStr = window.XMLHttpRequest.toString();
      if (xhrStr.length < 50 || xhrStr.includes('native code') === false) {
        detectionScore++;
        detectionResults.push('Modified XMLHttpRequest');
        console.log(`${debugPrefix} Detected modified XMLHttpRequest`);
      }
    } catch (e) {
      console.log(`${debugPrefix} Error checking API modifications:`, e);
    }

    // Test 3: Check for extension-specific DOM modifications and attributes
    try {
      const extensionSelectors = [
        '[data-adblock]', '[data-ublock]', '[data-ghostery]', '[data-adguard]',
        '.adblock-detected', '.ublock-detected', '.ghostery-detected',
        '#adblock-notify', '#ublock-notify', '.adnauseam-detected',
        '[style*="display: none !important"]'
      ];

      for (const selector of extensionSelectors) {
        if (document.querySelector(selector)) {
          detectionScore++;
          detectionResults.push(`DOM marker: ${selector}`);
          console.log(`${debugPrefix} Found extension DOM marker: ${selector}`);
        }
      }
    } catch (e) {
      console.log(`${debugPrefix} Error checking DOM markers:`, e);
    }

    // Test 4: Check for CSS injection patterns
    try {
      let cssRuleCount = 0;
      for (let i = 0; i < document.styleSheets.length; i++) {
        try {
          const sheet = document.styleSheets[i];
          if (sheet.cssRules) {
            for (let j = 0; j < sheet.cssRules.length; j++) {
              const rule = sheet.cssRules[j];
              if (rule.cssText && (
                rule.cssText.includes('display: none !important') ||
                rule.cssText.includes('[class*="ad"]') ||
                rule.cssText.includes('[id*="ad"]') ||
                rule.cssText.includes('visibility: hidden !important')
              )) {
                cssRuleCount++;
              }
            }
          }
        } catch (e) {
          // Cross-origin stylesheets may throw errors
        }
      }

      if (cssRuleCount > 10) {
        detectionScore += 2;
        detectionResults.push(`CSS injection: ${cssRuleCount} suspicious rules`);
        console.log(`${debugPrefix} Found ${cssRuleCount} suspicious CSS rules`);
      }
    } catch (e) {
      console.log(`${debugPrefix} Error checking CSS rules:`, e);
    }

    // Test 5: Check for browser-specific ad blockers
    try {
      const userAgent = navigator.userAgent.toLowerCase();

      // Brave browser detection
      if (userAgent.includes('brave') || (window as any).navigator?.brave) {
        detectionScore++;
        detectionResults.push('Brave browser detected');
        console.log(`${debugPrefix} Brave browser detected`);
      }

      // Opera ad blocker
      if (userAgent.includes('opera') || userAgent.includes('opr')) {
        detectionScore++;
        detectionResults.push('Opera browser (built-in ad blocker)');
        console.log(`${debugPrefix} Opera browser detected`);
      }

      // Edge tracking prevention
      if (userAgent.includes('edge') || userAgent.includes('edg')) {
        detectionScore++;
        detectionResults.push('Edge browser (tracking prevention)');
        console.log(`${debugPrefix} Edge browser detected`);
      }
    } catch (e) {
      console.log(`${debugPrefix} Error checking browser features:`, e);
    }

    // Test 6: Check for extension content scripts
    try {
      const extensionScripts = document.querySelectorAll('script[src*="extension://"], script[src*="moz-extension://"]');
      if (extensionScripts.length > 0) {
        detectionScore += 2;
        detectionResults.push(`Extension scripts: ${extensionScripts.length} found`);
        console.log(`${debugPrefix} Found ${extensionScripts.length} extension scripts`);
      }
    } catch (e) {
      console.log(`${debugPrefix} Error checking extension scripts:`, e);
    }

    // Test 7: Check for performance timing anomalies
    try {
      if (window.performance && window.performance.getEntriesByType) {
        const resources = window.performance.getEntriesByType('resource');
        const blockedResources = resources.filter(resource =>
          resource.duration === 0 && resource.transferSize === 0
        );

        if (blockedResources.length > 5) {
          detectionScore++;
          detectionResults.push(`Performance anomalies: ${blockedResources.length} blocked resources`);
          console.log(`${debugPrefix} Found ${blockedResources.length} blocked resources`);
        }
      }
    } catch (e) {
      console.log(`${debugPrefix} Error checking performance:`, e);
    }

    setTimeout(() => {
      const threshold = 2; // Require at least 2 positive detections
      const result = detectionScore >= threshold;
      console.log(`${debugPrefix} Advanced detection complete: ${detectionScore}/${maxScore} score, threshold: ${threshold}, result: ${result}`);
      console.log(`${debugPrefix} Detection methods triggered:`, detectionResults);
      resolve(result);
    }, 200);
  });
}

/**
 * Test for network request blocking
 * Attempts to make requests to known ad-serving domains
 */
function testNetworkBlocking(): Promise<boolean> {
  return new Promise((resolve) => {
    const testUrls = [
      'https://googleads.g.doubleclick.net/pagead/ads',
      'https://tpc.googlesyndication.com/simgad',
      'https://pagead2.googlesyndication.com/pagead/gen_204',
      'https://static.doubleclick.net/instream/ad_status.js'
    ];

    let blockedCount = 0;
    let completedTests = 0;

    testUrls.forEach((url) => {
      const img = new Image();

      const timeout = setTimeout(() => {
        blockedCount++;
        completedTests++;
        if (completedTests === testUrls.length) {
          // Require at least 2 blocked requests to reduce false positives
          resolve(blockedCount >= 2);
        }
      }, 3000);

      img.onload = () => {
        clearTimeout(timeout);
        completedTests++;
        if (completedTests === testUrls.length) {
          resolve(blockedCount >= 2);
        }
      };

      img.onerror = () => {
        clearTimeout(timeout);
        blockedCount++;
        completedTests++;
        if (completedTests === testUrls.length) {
          resolve(blockedCount >= 2);
        }
      };

      img.src = url + '?t=' + Date.now();
    });
  });
}

/**
 * Test if Brave Shields are actively blocking content
 */
function testBraveShieldsActive(): Promise<boolean> {
  return new Promise((resolve) => {
    // Test Brave-specific blocking patterns
    const testScript = document.createElement('script');
    testScript.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';

    const timeout = setTimeout(() => {
      resolve(true); // Likely blocked
    }, 2000);

    testScript.onload = () => {
      clearTimeout(timeout);
      resolve(false); // Not blocked
    };

    testScript.onerror = () => {
      clearTimeout(timeout);
      resolve(true); // Blocked
    };

    document.head.appendChild(testScript);

    // Clean up
    setTimeout(() => {
      try {
        if (document.head.contains(testScript)) {
          document.head.removeChild(testScript);
        }
      } catch (e) {
        // Ignore cleanup errors
      }
    }, 2500);
  });
}

/**
 * Test if Firefox Enhanced Tracking Protection is blocking
 */
function testFirefoxTrackingProtection(): Promise<boolean> {
  return new Promise((resolve) => {
    // Test tracking script that Firefox ETP typically blocks
    const testScript = document.createElement('script');
    testScript.src = 'https://www.google-analytics.com/analytics.js';

    const timeout = setTimeout(() => {
      resolve(true); // Likely blocked by ETP
    }, 2000);

    testScript.onload = () => {
      clearTimeout(timeout);
      resolve(false); // Not blocked
    };

    testScript.onerror = () => {
      clearTimeout(timeout);
      resolve(true); // Blocked
    };

    document.head.appendChild(testScript);

    // Clean up
    setTimeout(() => {
      try {
        if (document.head.contains(testScript)) {
          document.head.removeChild(testScript);
        }
      } catch (e) {
        // Ignore cleanup errors
      }
    }, 2500);
  });
}

/**
 * Test if Edge Tracking Prevention is blocking
 */
function testEdgeTrackingPrevention(): Promise<boolean> {
  return new Promise((resolve) => {
    // Test tracking resources that Edge typically blocks
    const testImg = new Image();

    const timeout = setTimeout(() => {
      resolve(true); // Likely blocked
    }, 2000);

    testImg.onload = () => {
      clearTimeout(timeout);
      resolve(false); // Not blocked
    };

    testImg.onerror = () => {
      clearTimeout(timeout);
      resolve(true); // Blocked
    };

    testImg.src = 'https://www.facebook.com/tr?id=test&ev=PageView&noscript=1';
  });
}

/**
 * Test if Opera Ad Blocker is blocking
 */
function testOperaAdBlocker(): Promise<boolean> {
  return new Promise((resolve) => {
    // Test ad resources that Opera typically blocks
    const testScript = document.createElement('script');
    testScript.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';

    const timeout = setTimeout(() => {
      resolve(true); // Likely blocked
    }, 2000);

    testScript.onload = () => {
      clearTimeout(timeout);
      resolve(false); // Not blocked
    };

    testScript.onerror = () => {
      clearTimeout(timeout);
      resolve(true); // Blocked
    };

    document.head.appendChild(testScript);

    // Clean up
    setTimeout(() => {
      try {
        if (document.head.contains(testScript)) {
          document.head.removeChild(testScript);
        }
      } catch (e) {
        // Ignore cleanup errors
      }
    }, 2500);
  });
}

/**
 * Try to identify specific ad blocker extensions based on detection patterns
 */
async function identifySpecificExtension(elementHiding: boolean, scriptBlocking: boolean, networkBlocking: boolean): Promise<AdBlockerInfo | null> {
  // Test for specific extension signatures

  // uBlock Origin specific test
  if (elementHiding && scriptBlocking && networkBlocking) {
    const uBlockTest = await testForUBlockOrigin();
    if (uBlockTest) {
      return {
        name: AD_BLOCKER_SIGNATURES.uBlockOrigin.name,
        type: AD_BLOCKER_SIGNATURES.uBlockOrigin.type,
        detected: true,
        confidence: 90,
        detectionMethod: 'uBlock Origin Signature Detection',
        disableInstructions: AD_BLOCKER_SIGNATURES.uBlockOrigin.disableInstructions,
        whitelistInstructions: AD_BLOCKER_SIGNATURES.uBlockOrigin.whitelistInstructions
      };
    }
  }

  // AdBlock Plus specific test
  if (elementHiding && scriptBlocking) {
    const adBlockPlusTest = await testForAdBlockPlus();
    if (adBlockPlusTest) {
      return {
        name: AD_BLOCKER_SIGNATURES.adBlockPlus.name,
        type: AD_BLOCKER_SIGNATURES.adBlockPlus.type,
        detected: true,
        confidence: 85,
        detectionMethod: 'AdBlock Plus Signature Detection',
        disableInstructions: AD_BLOCKER_SIGNATURES.adBlockPlus.disableInstructions,
        whitelistInstructions: AD_BLOCKER_SIGNATURES.adBlockPlus.whitelistInstructions
      };
    }
  }

  // AdGuard specific test
  if (elementHiding && networkBlocking) {
    const adGuardTest = await testForAdGuard();
    if (adGuardTest) {
      return {
        name: AD_BLOCKER_SIGNATURES.adGuard.name,
        type: AD_BLOCKER_SIGNATURES.adGuard.type,
        detected: true,
        confidence: 85,
        detectionMethod: 'AdGuard Signature Detection',
        disableInstructions: AD_BLOCKER_SIGNATURES.adGuard.disableInstructions,
        whitelistInstructions: AD_BLOCKER_SIGNATURES.adGuard.whitelistInstructions
      };
    }
  }

  return null;
}

/**
 * Test for uBlock Origin specific patterns
 */
async function testForUBlockOrigin(): Promise<boolean> {
  try {
    // uBlock Origin has very aggressive element hiding
    const testDiv = document.createElement('div');
    testDiv.className = 'adsbygoogle';
    testDiv.style.position = 'absolute';
    testDiv.style.top = '0px';
    testDiv.style.left = '0px';
    testDiv.style.width = '1px';
    testDiv.style.height = '1px';
    document.body.appendChild(testDiv);

    await new Promise(resolve => setTimeout(resolve, 100));

    const isHidden = window.getComputedStyle(testDiv).display === 'none';
    document.body.removeChild(testDiv);

    return isHidden;
  } catch (error) {
    return false;
  }
}

/**
 * Test for AdBlock Plus specific patterns
 */
async function testForAdBlockPlus(): Promise<boolean> {
  try {
    // AdBlock Plus specific element hiding pattern
    const testDiv = document.createElement('div');
    testDiv.className = 'ad-banner';
    testDiv.style.position = 'absolute';
    testDiv.style.top = '0px';
    testDiv.style.left = '0px';
    testDiv.style.width = '1px';
    testDiv.style.height = '1px';
    document.body.appendChild(testDiv);

    await new Promise(resolve => setTimeout(resolve, 100));

    const computedStyle = window.getComputedStyle(testDiv);
    const isHidden = computedStyle.display === 'none' || computedStyle.visibility === 'hidden';
    document.body.removeChild(testDiv);

    return isHidden;
  } catch (error) {
    return false;
  }
}

/**
 * Test for AdGuard specific patterns
 */
async function testForAdGuard(): Promise<boolean> {
  try {
    // AdGuard has specific network blocking patterns
    const testImg = new Image();
    let blocked = false;

    const timeout = setTimeout(() => {
      blocked = true;
    }, 1500);

    testImg.onload = () => {
      clearTimeout(timeout);
      blocked = false;
    };

    testImg.onerror = () => {
      clearTimeout(timeout);
      blocked = true;
    };

    testImg.src = 'https://googleads.g.doubleclick.net/pagead/ads?test=1';

    await new Promise(resolve => setTimeout(resolve, 1600));

    return blocked;
  } catch (error) {
    return false;
  }
}

/**
 * Detect browser-specific ad blocking features
 * Only detects when actual blocking behavior is confirmed
 */
async function detectBrowserFeatures(): Promise<AdBlockerInfo[]> {
  const detected: AdBlockerInfo[] = [];
  const userAgent = navigator.userAgent.toLowerCase();

  // Brave Browser Detection - only if shields are actually active
  if (userAgent.includes('brave') || (window as any).navigator?.brave) {
    // Test if Brave Shields are actually blocking content
    const isBraveBlocking = await testBraveShieldsActive();
    if (isBraveBlocking) {
      detected.push({
        name: AD_BLOCKER_SIGNATURES.braveShields.name,
        type: AD_BLOCKER_SIGNATURES.braveShields.type,
        detected: true,
        confidence: 85,
        detectionMethod: 'Brave Shields Active Test',
        disableInstructions: AD_BLOCKER_SIGNATURES.braveShields.disableInstructions,
        whitelistInstructions: AD_BLOCKER_SIGNATURES.braveShields.whitelistInstructions
      });
    }
  }

  // Firefox Enhanced Tracking Protection - only if actually blocking
  if (userAgent.includes('firefox')) {
    const isFirefoxBlocking = await testFirefoxTrackingProtection();
    if (isFirefoxBlocking) {
      detected.push({
        name: AD_BLOCKER_SIGNATURES.firefoxTracking.name,
        type: AD_BLOCKER_SIGNATURES.firefoxTracking.type,
        detected: true,
        confidence: 75,
        detectionMethod: 'Firefox ETP Active Test',
        disableInstructions: AD_BLOCKER_SIGNATURES.firefoxTracking.disableInstructions,
        whitelistInstructions: AD_BLOCKER_SIGNATURES.firefoxTracking.whitelistInstructions
      });
    }
  }

  // Edge Tracking Prevention
  if (userAgent.includes('edg/')) {
    const isEdgeBlocking = await testEdgeTrackingPrevention();
    if (isEdgeBlocking) {
      detected.push({
        name: 'Microsoft Edge Tracking Prevention',
        type: 'browser' as const,
        detected: true,
        confidence: 75,
        detectionMethod: 'Edge Tracking Prevention Test',
        disableInstructions: [
          'Click the three dots menu in Edge',
          'Go to Settings > Privacy, search, and services',
          'Under "Tracking prevention", select "Basic" or turn off',
          'Refresh the page to continue watching'
        ],
        whitelistInstructions: [
          'Click the shield icon in the Edge address bar',
          'Turn off "Tracking prevention" for this site',
          'Refresh the page to continue watching'
        ]
      });
    }
  }

  // Opera Ad Blocker
  if (userAgent.includes('opr/') || userAgent.includes('opera')) {
    const isOperaBlocking = await testOperaAdBlocker();
    if (isOperaBlocking) {
      detected.push({
        name: AD_BLOCKER_SIGNATURES.operaAdBlocker.name,
        type: AD_BLOCKER_SIGNATURES.operaAdBlocker.type,
        detected: true,
        confidence: 80,
        detectionMethod: 'Opera Ad Blocker Test',
        disableInstructions: AD_BLOCKER_SIGNATURES.operaAdBlocker.disableInstructions,
        whitelistInstructions: AD_BLOCKER_SIGNATURES.operaAdBlocker.whitelistInstructions
      });
    }
  }

  return detected;
}

/**
 * Main ad blocker detection function
 * Runs multiple detection methods and returns comprehensive results
 */
export async function detectAdBlockers(): Promise<AdBlockerDetectionResult> {
  const detectedBlockers: AdBlockerInfo[] = [];
  let totalConfidence = 0;

  // Enhanced production debugging
  const isProduction = import.meta.env.PROD;
  const debugPrefix = isProduction ? '🔍 [PROD]' : '🔍 [DEV]';

  console.log(`${debugPrefix} Starting ad blocker detection...`);
  console.log(`${debugPrefix} Environment:`, {
    isProduction,
    userAgent: navigator.userAgent,
    location: window.location.href,
    timestamp: new Date().toISOString()
  });

  try {
    console.log(`${debugPrefix} Running detection tests in parallel...`);

    // Run all detection tests in parallel with individual error handling
    // Include Vercel-compatible detection for production environments
    const detectionPromises = [
      testElementHiding().catch(err => {
        console.error(`${debugPrefix} Element hiding test failed:`, err);
        return false;
      }),
      testScriptBlocking().catch(err => {
        console.error(`${debugPrefix} Script blocking test failed:`, err);
        return false;
      }),
      testNetworkBlocking().catch(err => {
        console.error(`${debugPrefix} Network blocking test failed:`, err);
        return false;
      }),
      detectBrowserFeatures().catch(err => {
        console.error(`${debugPrefix} Browser features test failed:`, err);
        return [];
      })
    ];

    // Add advanced detection for production
    if (isProduction) {
      detectionPromises.push(
        testAdvancedAdBlockerDetection().catch(err => {
          console.error(`${debugPrefix} Advanced detection test failed:`, err);
          return false;
        })
      );
    }

    const results = await Promise.allSettled(detectionPromises);

    // Extract results from settled promises
    const elementHidingResult = results[0].status === 'fulfilled' ? results[0].value : false;
    const scriptBlockingResult = results[1].status === 'fulfilled' ? results[1].value : false;
    const networkBlockingResult = results[2].status === 'fulfilled' ? results[2].value : false;
    const browserBlockersResult = results[3].status === 'fulfilled' ? results[3].value : [];
    const advancedDetectionResult = isProduction && results[4] ?
      (results[4].status === 'fulfilled' ? results[4].value : false) : false;

    console.log(`${debugPrefix} Detection results:`, {
      elementHiding: elementHidingResult,
      scriptBlocking: scriptBlockingResult,
      networkBlocking: networkBlockingResult,
      browserBlockers: browserBlockersResult.length,
      advancedDetection: advancedDetectionResult
    });

    // Add browser-specific detections
    detectedBlockers.push(...browserBlockersResult);

    // Analyze results and determine specific ad blockers
    if (elementHidingResult || scriptBlockingResult || networkBlockingResult || advancedDetectionResult) {
      let confidence = 0;
      let detectionMethods: string[] = [];

      if (elementHidingResult) {
        confidence += 30;
        detectionMethods.push('Element Hiding');
      }
      if (scriptBlockingResult) {
        confidence += 40;
        detectionMethods.push('Script Blocking');
      }
      if (networkBlockingResult) {
        confidence += 30;
        detectionMethods.push('Network Blocking');
      }
      if (advancedDetectionResult) {
        confidence += 35;
        detectionMethods.push('Advanced Detection');
      }

      console.log(`${debugPrefix} Detection methods triggered:`, detectionMethods);
      console.log(`${debugPrefix} Total confidence:`, confidence);

      // Try to identify specific extensions based on detection patterns
      const specificExtension = await identifySpecificExtension(elementHidingResult, scriptBlockingResult, networkBlockingResult);

      if (specificExtension) {
        detectedBlockers.push(specificExtension);
      } else if (confidence >= 70 && browserBlockers.length === 0) {
        // Generic extension detection as fallback
        detectedBlockers.push({
          name: 'Browser Extension Ad Blocker',
          type: 'extension',
          detected: true,
          confidence: Math.min(confidence, 95),
          detectionMethod: detectionMethods.join(', '),
          disableInstructions: [
            'Look for an ad blocker icon in your browser toolbar',
            'Click the icon and disable the ad blocker for this site',
            'Common ad blockers include uBlock Origin, Adblock Plus, AdBlock',
            'Refresh the page after disabling'
          ],
          whitelistInstructions: [
            'Look for an ad blocker icon in your browser toolbar',
            'Click the icon and add this site to your whitelist',
            'Look for options like "Don\'t run on this site" or "Trust this site"',
            'Refresh the page after whitelisting'
          ]
        });
      }
    }

    // Calculate total confidence
    totalConfidence = detectedBlockers.reduce((sum, blocker) => sum + blocker.confidence, 0) / Math.max(detectedBlockers.length, 1);

    const result = {
      hasAdBlocker: detectedBlockers.length > 0,
      detectedBlockers,
      totalConfidence,
      detectionTimestamp: Date.now()
    };

    console.log(`${debugPrefix} Final detection result:`, result);

    return result;

  } catch (error) {
    console.error(`${debugPrefix} Ad blocker detection failed:`, error);
    console.error(`${debugPrefix} Error stack:`, error instanceof Error ? error.stack : 'No stack trace');

    const errorResult = {
      hasAdBlocker: false,
      detectedBlockers: [],
      totalConfidence: 0,
      detectionTimestamp: Date.now()
    };

    console.log(`${debugPrefix} Returning error result:`, errorResult);
    return errorResult;
  }
}

/**
 * Quick ad blocker check (faster, less comprehensive)
 * Useful for initial checks before loading video content
 */
export async function quickAdBlockerCheck(): Promise<boolean> {
  const isProduction = import.meta.env.PROD;
  const debugPrefix = isProduction ? '⚡ [PROD]' : '⚡ [DEV]';

  console.log(`${debugPrefix} Starting quick ad blocker check...`);

  try {
    // More comprehensive quick test elements
    const testElements = [
      'ads', 'advertisement', 'ad-banner', 'google-ads', 'adsbygoogle',
      'sponsored', 'promoted', 'banner', 'popup-ad', 'tracking'
    ];
    let hiddenCount = 0;

    // Quick global checks first
    let quickDetectionScore = 0;

    // Check for obvious ad blocker globals
    if (typeof (window as any).uBlock !== 'undefined' ||
        typeof (window as any).adblock !== 'undefined' ||
        typeof (window as any).AdBlock !== 'undefined') {
      quickDetectionScore++;
      console.log(`${debugPrefix} Found ad blocker globals`);
    }

    // Check user agent for known ad blocking browsers
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('brave') || userAgent.includes('opera')) {
      quickDetectionScore++;
      console.log(`${debugPrefix} Detected ad-blocking browser`);
    }

    // If we already have strong indicators, return early
    if (quickDetectionScore >= 1) {
      console.log(`${debugPrefix} Quick check positive: ${quickDetectionScore} indicators`);
      return true;
    }

    for (const className of testElements) {
      const testDiv = document.createElement('div');
      testDiv.className = className;
      // Position in visible area but make tiny
      testDiv.style.position = 'absolute';
      testDiv.style.top = '0px';
      testDiv.style.left = '0px';
      testDiv.style.width = '1px';
      testDiv.style.height = '1px';
      testDiv.style.overflow = 'hidden';
      testDiv.style.zIndex = '-1000';
      testDiv.innerHTML = '&nbsp;';

      document.body.appendChild(testDiv);

      await new Promise(resolve => setTimeout(resolve, 100));

      const computedStyle = window.getComputedStyle(testDiv);
      const isActivelyHidden = (
        computedStyle.display === 'none' ||
        computedStyle.visibility === 'hidden' ||
        computedStyle.opacity === '0' ||
        (testDiv.offsetHeight === 0 && testDiv.offsetWidth === 0 && computedStyle.height !== '0px')
      );

      if (isActivelyHidden) {
        hiddenCount++;
      }

      document.body.removeChild(testDiv);
    }

    // Lower threshold for production - any hidden element indicates ad blocker
    const threshold = isProduction ? 1 : 2;
    const result = hiddenCount >= threshold;

    console.log(`${debugPrefix} Quick check complete: ${hiddenCount}/${testElements.length} hidden, threshold: ${threshold}, result: ${result}`);
    return result;
  } catch (error) {
    console.error('Quick ad blocker check failed:', error);
    return false;
  }
}

/**
 * Re-check ad blocker status
 * Used when user claims to have disabled their ad blocker
 */
export async function recheckAdBlockers(): Promise<boolean> {
  const result = await detectAdBlockers();
  return !result.hasAdBlocker;
}

/**
 * Attempt one-click disable guidance for detected ad blockers
 * Provides smart guidance and direct links where possible
 */
export async function attemptOneClickDisable(detectedBlockers: AdBlockerInfo[]): Promise<string> {
  if (detectedBlockers.length === 0) {
    return 'No ad blockers detected to disable.';
  }

  const primaryBlocker = detectedBlockers[0];

  // Generate specific guidance based on the detected ad blocker
  switch (primaryBlocker.name) {
    case 'uBlock Origin':
      return await guideUBlockOriginDisable();

    case 'Adblock Plus':
      return await guideAdBlockPlusDisable();

    case 'AdBlock':
      return await guideAdBlockDisable();

    case 'AdGuard':
      return await guideAdGuardDisable();

    case 'Brave Shields':
      return await guideBraveShieldsDisable();

    case 'Firefox Enhanced Tracking Protection':
      return await guideFirefoxETPDisable();

    case 'Microsoft Edge Tracking Prevention':
      return await guideEdgeTrackingDisable();

    case 'Opera Ad Blocker':
      return await guideOperaAdBlockerDisable();

    default:
      return await guideGenericAdBlockerDisable(primaryBlocker);
  }
}

/**
 * Guide for disabling uBlock Origin
 */
async function guideUBlockOriginDisable(): Promise<string> {
  // Try to open extension page directly
  try {
    const extensionUrl = 'chrome-extension://cjpalhdlnbpafiamejdnhcphjbkeiagm/popup.html';
    window.open(extensionUrl, '_blank');
    return 'Opening uBlock Origin settings... Click the large power button to disable it for this site.';
  } catch (error) {
    return 'Look for the uBlock Origin icon (red shield) in your browser toolbar and click the power button to disable it.';
  }
}

/**
 * Guide for disabling AdBlock Plus
 */
async function guideAdBlockPlusDisable(): Promise<string> {
  return 'Look for the AdBlock Plus icon (red stop sign) in your browser toolbar. Click it and select "Enabled on this site" to disable it.';
}

/**
 * Guide for disabling AdBlock
 */
async function guideAdBlockDisable(): Promise<string> {
  return 'Look for the AdBlock icon (red hand) in your browser toolbar. Click it and select "Pause AdBlock" or "Don\'t run on this site".';
}

/**
 * Guide for disabling AdGuard
 */
async function guideAdGuardDisable(): Promise<string> {
  return 'Look for the AdGuard icon (green shield) in your browser toolbar. Click it and select "Pause protection on this website".';
}

/**
 * Guide for disabling Brave Shields
 */
async function guideBraveShieldsDisable(): Promise<string> {
  return 'Click the Brave Shields icon (orange/red shield) in the address bar and toggle "Shields" to "Down" for this site.';
}

/**
 * Guide for disabling Firefox Enhanced Tracking Protection
 */
async function guideFirefoxETPDisable(): Promise<string> {
  return 'Click the shield icon in the Firefox address bar and select "Turn off Blocking for This Site".';
}

/**
 * Guide for disabling Edge Tracking Prevention
 */
async function guideEdgeTrackingDisable(): Promise<string> {
  return 'Click the shield icon in the Edge address bar and turn off "Tracking prevention" for this site.';
}

/**
 * Guide for disabling Opera Ad Blocker
 */
async function guideOperaAdBlockerDisable(): Promise<string> {
  return 'Click the shield icon in the Opera address bar and select "Turn off ad blocker for this site".';
}

/**
 * Guide for disabling generic ad blockers
 */
async function guideGenericAdBlockerDisable(blocker: AdBlockerInfo): Promise<string> {
  return `Look for an ad blocker icon in your browser toolbar (${blocker.name}). Click it and look for options to disable or whitelist this site.`;
}
