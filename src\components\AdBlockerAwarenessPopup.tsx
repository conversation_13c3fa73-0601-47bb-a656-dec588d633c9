import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface AdBlockerAwarenessPopupProps {
  isVisible: boolean;
  onDismiss: () => void;
  autoCloseDelay?: number; // in milliseconds
}

const AdBlockerAwarenessPopup: React.FC<AdBlockerAwarenessPopupProps> = ({
  isVisible,
  onDismiss,
  autoCloseDelay = 17000 // 17 seconds default
}) => {
  const [isAnimatingOut, setIsAnimatingOut] = useState(false);

  // Auto-dismiss functionality
  useEffect(() => {
    if (!isVisible) return;

    const autoCloseTimer = setTimeout(() => {
      handleDismiss();
    }, autoCloseDelay);

    return () => clearTimeout(autoCloseTimer);
  }, [isVisible, autoCloseDelay]);

  const handleDismiss = () => {
    setIsAnimatingOut(true);
    // Wait for animation to complete before calling onDismiss
    setTimeout(() => {
      onDismiss();
      setIsAnimatingOut(false);
    }, 300); // Match animation duration
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Backdrop - very subtle, non-blocking */}
      <div 
        className={`fixed inset-0 z-40 transition-opacity duration-300 ${
          isAnimatingOut ? 'opacity-0' : 'opacity-100'
        }`}
        style={{ 
          background: 'rgba(0, 0, 0, 0.1)',
          pointerEvents: 'none' // Allow clicks through backdrop
        }}
      />

      {/* Popup Container */}
      <div className="fixed inset-0 z-50 pointer-events-none">
        <div className="flex items-start justify-center pt-12 sm:pt-16 md:pt-20 px-4">
          <Card
            className={`
              relative max-w-xl w-full pointer-events-auto
              bg-card/95 backdrop-blur-sm border-border/50 shadow-2xl
              transition-all duration-300 ease-out
              ${isAnimatingOut
                ? 'opacity-0 scale-95 translate-y-[-10px]'
                : 'opacity-100 scale-100 translate-y-0'
              }
            `}
            style={{
              background: 'rgba(10, 10, 10, 0.95)', // Match dark theme
              borderColor: 'rgba(230, 203, 142, 0.2)' // Subtle primary border
            }}
          >
            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="absolute right-3 top-3 h-10 w-10 p-0 hover:bg-primary/10 text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Close popup"
            >
              <X className="h-5 w-5" />
            </Button>

            <CardContent className="p-10 pr-16">
              {/* Header with Icon */}
              <div className="flex items-center gap-4 mb-6">
                <div className="p-3 rounded-full bg-red-500/20">
                  {/* Filled Heart Icon */}
                  <svg className="w-7 h-7 text-red-500 fill-current" viewBox="0 0 24 24">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-foreground">
                    Supporting Free Content
                  </h3>
                  <p className="text-base text-muted-foreground">
                    A quick note about our service
                  </p>
                </div>
              </div>

              {/* Main Message */}
              <div className="space-y-4 text-base text-muted-foreground">
                <p className="leading-relaxed">
                  Hi there! 👋 We keep this streaming service completely free by showing
                  a few ads that help cover our server costs and storage expenses.
                </p>

                <p className="leading-relaxed">
                  For the best experience and to support our free content, consider
                  <span className="text-primary font-medium"> whitelisting our site</span> in
                  your ad blocker settings.
                </p>

                <p className="text-sm text-muted-foreground/80 italic">
                  Don't worry - you can still use the site normally! This is just a friendly reminder. 😊
                </p>
              </div>

              {/* Optional Action Button */}
              <div className="mt-6 flex justify-end">
                <Button
                  variant="outline"
                  size="default"
                  onClick={handleDismiss}
                  className="text-sm border-primary/20 hover:border-primary/40 hover:bg-primary/5 px-6 py-2"
                >
                  Got it, thanks!
                </Button>
              </div>

              {/* Auto-close indicator */}
              <div className="mt-4 flex items-center justify-center gap-2 text-sm text-muted-foreground/60">
                {/* Filled Info Icon */}
                <svg className="w-4 h-4 text-blue-500 fill-current" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                </svg>
                <span>This message will auto-close in a few seconds</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default AdBlockerAwarenessPopup;
