/**
 * TMDB Integration Verification Script
 * 
 * Quick verification script to test TMDB API integration
 * Run this in browser console to verify everything is working
 */

import { 
  getComprehensiveContentData, 
  searchContent, 
  isValidTMDBId,
  buildImageUrl 
} from '@/services/tmdbService';

// Quick verification function
export async function verifyTMDBIntegration() {
  console.group('🎬 TMDB Integration Verification');
  
  const results = {
    apiKey: false,
    validation: false,
    movieFetch: false,
    tvFetch: false,
    search: false,
    imageUrls: false,
    errors: [] as string[]
  };

  try {
    // 1. Check API key configuration
    console.log('1. Checking API key configuration...');
    const apiKey = import.meta.env.VITE_TMDB_API_KEY;
    if (apiKey && apiKey.length > 0) {
      results.apiKey = true;
      console.log('✅ API key configured');
    } else {
      results.errors.push('API key not configured');
      console.log('❌ API key not configured');
    }

    // 2. Test TMDB ID validation
    console.log('2. Testing TMDB ID validation...');
    if (isValidTMDBId('27205') && !isValidTMDBId('abc123')) {
      results.validation = true;
      console.log('✅ TMDB ID validation working');
    } else {
      results.errors.push('TMDB ID validation failed');
      console.log('❌ TMDB ID validation failed');
    }

    // 3. Test movie data fetching
    console.log('3. Testing movie data fetching...');
    try {
      const movieResult = await getComprehensiveContentData('27205', 'movie'); // Inception
      if (movieResult.success && movieResult.data.title) {
        results.movieFetch = true;
        console.log('✅ Movie data fetching working:', movieResult.data.title);
      } else {
        results.errors.push('Movie data fetching failed');
        console.log('❌ Movie data fetching failed');
      }
    } catch (error) {
      results.errors.push(`Movie fetch error: ${error}`);
      console.log('❌ Movie data fetching error:', error);
    }

    // 4. Test TV show data fetching
    console.log('4. Testing TV show data fetching...');
    try {
      const tvResult = await getComprehensiveContentData('1396', 'tv'); // Breaking Bad
      if (tvResult.success && tvResult.data.title) {
        results.tvFetch = true;
        console.log('✅ TV show data fetching working:', tvResult.data.title);
      } else {
        results.errors.push('TV show data fetching failed');
        console.log('❌ TV show data fetching failed');
      }
    } catch (error) {
      results.errors.push(`TV fetch error: ${error}`);
      console.log('❌ TV show data fetching error:', error);
    }

    // 5. Test search functionality
    console.log('5. Testing search functionality...');
    try {
      const searchResult = await searchContent('Inception', 'movie');
      if (searchResult.results && searchResult.results.length > 0) {
        results.search = true;
        console.log('✅ Search functionality working:', searchResult.results.length, 'results');
      } else {
        results.errors.push('Search functionality failed');
        console.log('❌ Search functionality failed');
      }
    } catch (error) {
      results.errors.push(`Search error: ${error}`);
      console.log('❌ Search functionality error:', error);
    }

    // 6. Test image URL building
    console.log('6. Testing image URL building...');
    const posterUrl = buildImageUrl('/test.jpg', 'poster', 'large');
    const backdropUrl = buildImageUrl('/backdrop.jpg', 'backdrop', 'original');
    if (posterUrl && backdropUrl && posterUrl.includes('test.jpg')) {
      results.imageUrls = true;
      console.log('✅ Image URL building working');
    } else {
      results.errors.push('Image URL building failed');
      console.log('❌ Image URL building failed');
    }

  } catch (error) {
    results.errors.push(`General error: ${error}`);
    console.log('❌ General error:', error);
  }

  // Summary
  const totalTests = 6;
  const passedTests = Object.values(results).filter(v => v === true).length;
  const successRate = Math.round((passedTests / totalTests) * 100);

  console.log('\n📊 Verification Summary:');
  console.log(`   Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`   Success Rate: ${successRate}%`);
  
  if (results.errors.length > 0) {
    console.log('   Errors:');
    results.errors.forEach(error => console.log(`     - ${error}`));
  }

  if (successRate === 100) {
    console.log('🎉 TMDB integration is fully functional!');
  } else if (successRate >= 80) {
    console.log('✅ TMDB integration is mostly working. Minor issues detected.');
  } else {
    console.log('⚠️ TMDB integration has significant issues. Please review configuration.');
  }

  console.groupEnd();
  
  return {
    results,
    summary: {
      totalTests,
      passedTests,
      successRate,
      errors: results.errors
    }
  };
}

// Quick test function for admin panel
export async function quickTMDBTest() {
  console.log('🎬 Running quick TMDB test...');
  
  try {
    const result = await getComprehensiveContentData('27205'); // Inception
    if (result.success) {
      console.log('✅ TMDB API is working!');
      console.log('📽️ Test movie:', result.data.title);
      console.log('⭐ Rating:', result.data.imdbRating);
      console.log('🎭 Genres:', result.data.genres.join(', '));
      return true;
    } else {
      console.log('❌ TMDB API test failed:', result.error);
      return false;
    }
  } catch (error) {
    console.log('❌ TMDB API error:', error);
    return false;
  }
}

// Export for global access
(window as any).verifyTMDBIntegration = verifyTMDBIntegration;
(window as any).quickTMDBTest = quickTMDBTest;
