<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Ad Blocker Detection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a0a;
            color: #e6cb8e;
        }
        .test-result {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border: 1px solid #333;
        }
        .success { background: #1a4a1a; border-color: #4a8a4a; }
        .error { background: #4a1a1a; border-color: #8a4a4a; }
        .info { background: #1a1a4a; border-color: #4a4a8a; }
        .warning { background: #4a4a1a; border-color: #8a8a4a; }
        button {
            background: #e6cb8e;
            color: #0a0a0a;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
        }
        button:hover {
            background: #d4b876;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        pre {
            background: #1a1a1a;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 11px;
            max-height: 300px;
            overflow-y: auto;
        }
        .score {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .detection-positive {
            background: #4a1a1a;
            color: #ff6b6b;
            border: 2px solid #8a4a4a;
        }
        .detection-negative {
            background: #1a4a1a;
            color: #51cf66;
            border: 2px solid #4a8a4a;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <h1>🔍 Enhanced Ad Blocker Detection Test</h1>
    <p>This comprehensive test uses the new advanced detection methods designed for production environments.</p>
    
    <div>
        <button onclick="runQuickTest()" id="quickBtn">Quick Test</button>
        <button onclick="runAdvancedTest()" id="advancedBtn">Advanced Test</button>
        <button onclick="runElementTest()" id="elementBtn">Element Hiding Test</button>
        <button onclick="runFullSuite()" id="fullBtn">Full Test Suite</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="overallScore"></div>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        const overallScore = document.getElementById('overallScore');
        let totalDetectionScore = 0;
        let totalTests = 0;
        
        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${JSON.stringify(content, null, 2)}</pre>`;
            results.appendChild(div);
        }
        
        function updateOverallScore() {
            const percentage = totalTests > 0 ? (totalDetectionScore / totalTests * 100) : 0;
            const isDetected = percentage > 50;
            
            overallScore.innerHTML = `
                <div class="score ${isDetected ? 'detection-positive' : 'detection-negative'}">
                    ${isDetected ? '🚨 AD BLOCKER DETECTED' : '✅ NO AD BLOCKER DETECTED'}
                    <br>
                    Detection Score: ${totalDetectionScore}/${totalTests} (${percentage.toFixed(1)}%)
                </div>
            `;
        }
        
        function clearResults() {
            results.innerHTML = '';
            overallScore.innerHTML = '';
            totalDetectionScore = 0;
            totalTests = 0;
        }
        
        async function runQuickTest() {
            document.getElementById('quickBtn').disabled = true;
            console.log('🔍 Running quick ad blocker test...');
            
            let detectionScore = 0;
            const tests = [];
            
            // Test 1: Global variables
            const adBlockerGlobals = ['uBlock', 'adblock', 'AdBlock', 'adblockplus', 'ghostery', 'AdGuard'];
            let foundGlobals = [];
            
            for (const global of adBlockerGlobals) {
                if (typeof window[global] !== 'undefined') {
                    foundGlobals.push(global);
                    detectionScore++;
                }
            }
            
            tests.push({
                test: 'Global Variables',
                found: foundGlobals,
                score: foundGlobals.length
            });
            
            // Test 2: Browser detection
            const userAgent = navigator.userAgent.toLowerCase();
            let browserScore = 0;
            let detectedBrowsers = [];
            
            if (userAgent.includes('brave') || window.navigator?.brave) {
                browserScore++;
                detectedBrowsers.push('Brave');
            }
            if (userAgent.includes('opera') || userAgent.includes('opr')) {
                browserScore++;
                detectedBrowsers.push('Opera');
            }
            if (userAgent.includes('edge') || userAgent.includes('edg')) {
                browserScore++;
                detectedBrowsers.push('Edge');
            }
            
            detectionScore += browserScore;
            tests.push({
                test: 'Browser Detection',
                found: detectedBrowsers,
                score: browserScore
            });
            
            // Test 3: Quick element hiding
            const quickElements = ['ads', 'advertisement', 'google-ads', 'sponsored', 'banner'];
            let hiddenCount = 0;
            
            for (const className of quickElements) {
                const testDiv = document.createElement('div');
                testDiv.className = className;
                testDiv.style.cssText = `
                    position: absolute;
                    top: 0px;
                    left: 0px;
                    width: 1px;
                    height: 1px;
                    overflow: hidden;
                    z-index: -1000;
                `;
                testDiv.innerHTML = '&nbsp;';
                
                document.body.appendChild(testDiv);
                
                await new Promise(resolve => setTimeout(resolve, 50));
                
                const computedStyle = window.getComputedStyle(testDiv);
                const isHidden = computedStyle.display === 'none' ||
                               computedStyle.visibility === 'hidden' ||
                               computedStyle.opacity === '0' ||
                               testDiv.offsetHeight === 0;
                
                if (isHidden) {
                    hiddenCount++;
                }
                
                document.body.removeChild(testDiv);
            }
            
            if (hiddenCount > 0) {
                detectionScore += hiddenCount;
            }
            
            tests.push({
                test: 'Quick Element Hiding',
                hidden: hiddenCount,
                total: quickElements.length,
                score: hiddenCount
            });
            
            const result = detectionScore > 0;
            totalDetectionScore += detectionScore;
            totalTests += 1;
            
            addResult('Quick Test Results', {
                detectionScore,
                result,
                tests
            }, result ? 'error' : 'success');
            
            updateOverallScore();
            document.getElementById('quickBtn').disabled = false;
        }
        
        async function runAdvancedTest() {
            document.getElementById('advancedBtn').disabled = true;
            console.log('🔍 Running advanced ad blocker test...');
            
            let detectionScore = 0;
            const tests = [];
            
            // Test 1: Comprehensive global check
            const comprehensiveGlobals = [
                'uBlock', 'adblock', 'AdBlock', 'adblockplus', 'AdblockPlus',
                'ghostery', 'Ghostery', 'adguard', 'AdGuard', 'adnauseam',
                'disconnect', 'privacy', 'blockAdBlock', 'canRunAds', 'isAdBlockActive'
            ];
            
            let foundAdvancedGlobals = [];
            for (const global of comprehensiveGlobals) {
                if (typeof window[global] !== 'undefined') {
                    foundAdvancedGlobals.push(global);
                    detectionScore++;
                }
            }
            
            tests.push({
                test: 'Comprehensive Globals',
                found: foundAdvancedGlobals,
                score: foundAdvancedGlobals.length
            });
            
            // Test 2: API modification detection
            let apiModifications = [];
            
            try {
                const fetchStr = window.fetch.toString();
                if (fetchStr.length < 50 || !fetchStr.includes('native code')) {
                    apiModifications.push('fetch');
                    detectionScore++;
                }
                
                const xhrStr = window.XMLHttpRequest.toString();
                if (xhrStr.length < 50 || !xhrStr.includes('native code')) {
                    apiModifications.push('XMLHttpRequest');
                    detectionScore++;
                }
            } catch (e) {
                // Ignore errors
            }
            
            tests.push({
                test: 'API Modifications',
                found: apiModifications,
                score: apiModifications.length
            });
            
            // Test 3: CSS injection detection
            let cssRuleCount = 0;
            try {
                for (let i = 0; i < document.styleSheets.length; i++) {
                    try {
                        const sheet = document.styleSheets[i];
                        if (sheet.cssRules) {
                            for (let j = 0; j < sheet.cssRules.length; j++) {
                                const rule = sheet.cssRules[j];
                                if (rule.cssText && (
                                    rule.cssText.includes('display: none !important') ||
                                    rule.cssText.includes('[class*="ad"]') ||
                                    rule.cssText.includes('[id*="ad"]')
                                )) {
                                    cssRuleCount++;
                                }
                            }
                        }
                    } catch (e) {
                        // Cross-origin stylesheets
                    }
                }
                
                if (cssRuleCount > 10) {
                    detectionScore += 2;
                }
            } catch (e) {
                // Ignore errors
            }
            
            tests.push({
                test: 'CSS Injection',
                suspiciousRules: cssRuleCount,
                score: cssRuleCount > 10 ? 2 : 0
            });
            
            // Test 4: Extension script detection
            const extensionScripts = document.querySelectorAll('script[src*="extension://"], script[src*="moz-extension://"]');
            if (extensionScripts.length > 0) {
                detectionScore += 2;
            }
            
            tests.push({
                test: 'Extension Scripts',
                found: extensionScripts.length,
                score: extensionScripts.length > 0 ? 2 : 0
            });
            
            const result = detectionScore >= 2;
            totalDetectionScore += Math.min(detectionScore, 5); // Cap contribution
            totalTests += 1;
            
            addResult('Advanced Test Results', {
                detectionScore,
                result,
                tests
            }, result ? 'error' : 'success');
            
            updateOverallScore();
            document.getElementById('advancedBtn').disabled = false;
        }
        
        async function runElementTest() {
            document.getElementById('elementBtn').disabled = true;
            console.log('🔍 Running comprehensive element hiding test...');
            
            const comprehensiveElements = [
                // Common ad class names
                'ads', 'ad', 'advertisement', 'advert', 'ad-banner', 'ad-container',
                'ad-slot', 'ad-space', 'ad-unit', 'ad-wrapper', 'ad-block', 'ad-box',
                
                // Google Ads specific
                'google-ads', 'googleads', 'adsbygoogle', 'gpt-ad', 'dfp-ad',
                
                // Other networks
                'amazon-ads', 'facebook-ads', 'outbrain', 'taboola',
                
                // Sponsored content
                'sponsored', 'promoted', 'native-ad',
                
                // Banner types
                'banner', 'leaderboard', 'skyscraper', 'rectangle',
                
                // Tracking
                'tracking', 'analytics', 'pixel', 'beacon'
            ];
            
            let hiddenCount = 0;
            const elementResults = [];
            
            for (const className of comprehensiveElements) {
                const testDiv = document.createElement('div');
                testDiv.className = className;
                testDiv.style.cssText = `
                    position: absolute;
                    top: 0px;
                    left: 0px;
                    width: 1px;
                    height: 1px;
                    overflow: hidden;
                    z-index: -1000;
                `;
                testDiv.innerHTML = '&nbsp;';
                
                document.body.appendChild(testDiv);
                
                await new Promise(resolve => setTimeout(resolve, 50));
                
                const computedStyle = window.getComputedStyle(testDiv);
                const isHidden = computedStyle.display === 'none' ||
                               computedStyle.visibility === 'hidden' ||
                               computedStyle.opacity === '0' ||
                               (testDiv.offsetHeight === 0 && testDiv.offsetWidth === 0);
                
                if (isHidden) {
                    hiddenCount++;
                    elementResults.push({ className, hidden: true });
                } else {
                    elementResults.push({ className, hidden: false });
                }
                
                document.body.removeChild(testDiv);
            }
            
            const threshold = 0.2; // 20% threshold
            const result = (hiddenCount / comprehensiveElements.length) >= threshold;
            
            totalDetectionScore += result ? 3 : 0;
            totalTests += 1;
            
            addResult('Comprehensive Element Hiding Test', {
                hiddenCount,
                totalElements: comprehensiveElements.length,
                percentage: ((hiddenCount / comprehensiveElements.length) * 100).toFixed(1),
                threshold: (threshold * 100).toFixed(1),
                result,
                hiddenElements: elementResults.filter(e => e.hidden).map(e => e.className)
            }, result ? 'error' : 'success');
            
            updateOverallScore();
            document.getElementById('elementBtn').disabled = false;
        }
        
        async function runFullSuite() {
            clearResults();
            addResult('Starting Full Test Suite', { timestamp: new Date().toISOString() }, 'info');
            
            await runQuickTest();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await runAdvancedTest();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await runElementTest();
            
            addResult('Full Test Suite Complete', { 
                timestamp: new Date().toISOString(),
                finalScore: `${totalDetectionScore}/${totalTests}`,
                conclusion: totalDetectionScore > (totalTests / 2) ? 'AD BLOCKER DETECTED' : 'NO AD BLOCKER DETECTED'
            }, 'info');
        }
        
        // Auto-run quick test on page load
        window.addEventListener('load', () => {
            addResult('Page Loaded', {
                userAgent: navigator.userAgent,
                location: window.location.href,
                timestamp: new Date().toISOString()
            }, 'info');
            
            setTimeout(runQuickTest, 1000);
        });
    </script>
</body>
</html>
