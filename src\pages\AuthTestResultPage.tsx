import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Shield, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/Header';
import { scrollToTop } from '@/utils/scrollToTop';

interface TestResult {
  testName: string;
  passed: boolean;
  message: string;
  details?: any;
}

interface TestSuite {
  suiteName: string;
  results: TestResult[];
  passed: number;
  failed: number;
  total: number;
}

interface AuthTestResults {
  authSuite: TestSuite;
  securitySuite: TestSuite;
  overallPassed: boolean;
}

const AuthTestResultPage: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<AuthTestResults | null>(null);
  const [testStarted, setTestStarted] = useState(false);

  const runAuthTests = async () => {
    setIsRunning(true);
    setTestStarted(true);
    
    try {
      const { runAllAuthTests } = await import('@/utils/authTestUtils');
      const testResults = await runAllAuthTests();
      setResults(testResults);
    } catch (error) {
      console.error('Authentication tests failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const resetTest = () => {
    setResults(null);
    setTestStarted(false);
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusBadge = (passed: boolean) => {
    return (
      <Badge variant={passed ? "default" : "destructive"} className="ml-2">
        {passed ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-primary mb-2">
              Authentication System Tests
            </h1>
            <p className="text-muted-foreground">
              Comprehensive testing of authentication and security features
            </p>
          </div>
          
          <Link to="/admin" onClick={scrollToTop}>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Admin
            </Button>
          </Link>
        </div>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="w-5 h-5" />
              Authentication Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={runAuthTests}
                disabled={isRunning}
                className="flex-1"
              >
                {isRunning ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Running Auth Tests...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Run Authentication Tests
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={resetTest}
                disabled={isRunning}
                className="flex-1"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reset Test
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testStarted && results && (
          <>
            {/* Overall Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {results.overallPassed ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500" />
                  )}
                  Overall Test Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-green-500">
                      {results.authSuite.passed + results.securitySuite.passed}
                    </div>
                    <div className="text-sm text-muted-foreground">Tests Passed</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-red-500">
                      {results.authSuite.failed + results.securitySuite.failed}
                    </div>
                    <div className="text-sm text-muted-foreground">Tests Failed</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-primary">
                      {results.authSuite.total + results.securitySuite.total}
                    </div>
                    <div className="text-sm text-muted-foreground">Total Tests</div>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="w-4 h-4" />
                  <AlertDescription>
                    {results.overallPassed ? (
                      <span className="text-green-600">✅ All authentication tests passed! Security system is fully functional.</span>
                    ) : (
                      <span className="text-red-600">⚠️ Some authentication tests failed. Please review security implementation.</span>
                    )}
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Authentication Suite Results */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    {results.authSuite.failed === 0 ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-500" />
                    )}
                    🔐 {results.authSuite.suiteName}
                  </span>
                  <Badge variant="outline">
                    {results.authSuite.passed}/{results.authSuite.total} passed
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {results.authSuite.results.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(result.passed)}
                        <div>
                          <div className="font-medium">{result.testName}</div>
                          <div className="text-sm text-muted-foreground">{result.message}</div>
                        </div>
                      </div>
                      {getStatusBadge(result.passed)}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Security Suite Results */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    {results.securitySuite.failed === 0 ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-500" />
                    )}
                    🛡️ {results.securitySuite.suiteName}
                  </span>
                  <Badge variant="outline">
                    {results.securitySuite.passed}/{results.securitySuite.total} passed
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {results.securitySuite.results.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(result.passed)}
                        <div>
                          <div className="font-medium">{result.testName}</div>
                          <div className="text-sm text-muted-foreground">{result.message}</div>
                        </div>
                      </div>
                      {getStatusBadge(result.passed)}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="w-4 h-4" />
              <AlertDescription>
                <strong>How to run authentication tests:</strong>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Click "Run Authentication Tests" to test all security features</li>
                  <li>• Tests include session management, validation, security measures, and authentication flow</li>
                  <li>• Authentication suite tests core login and session functionality</li>
                  <li>• Security suite tests protection measures like XSS prevention and brute force protection</li>
                  <li>• Green indicators show secure features, red indicators show security issues</li>
                  <li>• Check browser console for detailed security logs and technical information</li>
                </ul>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthTestResultPage;
