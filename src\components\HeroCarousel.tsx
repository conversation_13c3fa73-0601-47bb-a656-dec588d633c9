
import * as React from "react";
import { Link } from "react-router-dom";
import { mediaData } from "@/data/movies";
import { MediaItem } from "@/types/media";
import { ChevronLeft, ChevronRight, Play } from "lucide-react";
import { getHomepageContent } from "@/utils/contentFilters";
import { scrollToTop } from "@/utils/scrollToTop";

// Get carousel content using the content filter utility
const { carousel: carouselContent } = getHomepageContent(mediaData, 20);

// Fallback to first 5 items if no carousel content is available
const featured = carouselContent.length > 0 ? carouselContent : mediaData.slice(0, 5);

export default function HeroCarousel() {
  const [idx, setIdx] = React.useState(0);

  React.useEffect(() => {
    const t = setInterval(() => {
      setIdx((v) => (v + 1) % featured.length);
    }, 5000);
    return () => clearInterval(t);
  }, []);

  const item = featured[idx];

  function handlePrev(e?: React.MouseEvent) {
    // Prevent bubbling focus to underlying layers
    e?.stopPropagation?.();
    setIdx((idx - 1 + featured.length) % featured.length);
  }
  function handleNext(e?: React.MouseEvent) {
    e?.stopPropagation?.();
    setIdx((idx + 1) % featured.length);
  }

  return (
    <section className="relative h-[280px] sm:h-[350px] md:h-[420px] lg:h-[490px] flex items-stretch w-full mb-6 sm:mb-8 rounded-lg overflow-hidden shadow animate-fade-in group">
      {/* Background image */}
      <img
        src={item.coverImage}
        alt={item.title}
        className="absolute inset-0 w-full h-full object-cover object-center brightness-75 transition-all duration-700 pointer-events-none select-none group-hover:brightness-50"
        draggable={false}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent pointer-events-none select-none" />

      {/* Clickable overlay for navigation */}
      <Link
        to={`/content/${item.id}`}
        onClick={scrollToTop}
        className="absolute inset-0 z-5 cursor-pointer"
        aria-label={`View ${item.title}`}
      />
      {/* Carousel controls */}
      <button
        className="absolute left-2 sm:left-3 md:left-4 top-1/2 -translate-y-1/2 bg-background/70 text-primary p-1.5 sm:p-2 rounded-full z-20 hover:bg-background hover:scale-110 transition shadow-lg border border-[#e6cb8e]/60 min-h-[44px] min-w-[44px] flex items-center justify-center"
        onClick={handlePrev}
        aria-label="Previous"
        tabIndex={0}
        style={{ outline: "none" }}
      >
        <ChevronLeft size={20} className="sm:w-7 sm:h-7" />
      </button>
      <button
        className="absolute right-2 sm:right-3 md:right-4 top-1/2 -translate-y-1/2 bg-background/70 text-primary p-1.5 sm:p-2 rounded-full z-20 hover:bg-background hover:scale-110 transition shadow-lg border border-[#e6cb8e]/60 min-h-[44px] min-w-[44px] flex items-center justify-center"
        onClick={handleNext}
        aria-label="Next"
        tabIndex={0}
        style={{ outline: "none" }}
      >
        <ChevronRight size={20} className="sm:w-7 sm:h-7" />
      </button>

      {/* Main content */}
      <div className="hero-carousel-content z-10 relative flex flex-col h-full justify-center pl-6 sm:pl-16 md:pl-20 max-w-2xl text-left pointer-events-none select-none">
        <span className="inline-block mb-3 px-3 py-1 bg-primary/80 text-primary-foreground rounded-full text-xs font-semibold uppercase tracking-widest pointer-events-auto select-auto">
          {item.type === "movie" ? "Movie" : "Web Series"}
        </span>
        <h2
          className="mobile-hero-title text-5xl md:text-6xl font-extrabold mb-2 drop-shadow-lg"
          style={{
            color: "#fff",
            textTransform: "uppercase",
            fontFamily: "'Koulen', Impact, Arial, sans-serif",
            letterSpacing: "0.08em",
            fontWeight: 400,
            lineHeight: 1.12,
          }}
        >
          {item.title}
        </h2>
        <p className="mobile-hero-description mb-3 text-base md:text-lg text-secondary-foreground max-w-lg drop-shadow pointer-events-auto select-auto"
          style={{
            color: "#fff",
            fontSize: "1em"
          }}
        >
          {item.description}
        </p>
        <div className="flex gap-2 flex-wrap pt-1 pointer-events-auto select-auto">
          {item.genres.map(g => (
            <span
              key={g}
              className="px-3 py-1 rounded text-xs font-black uppercase tracking-wide shadow"
              style={{
                background: "#e6cb8e",
                color: "#232323",
                fontSize: "0.92em",
                letterSpacing: "0.04em",
              }}
            >
              {g}
            </span>
          ))}
          <span className="text-xs inline-flex items-center text-muted-foreground ml-3" style={{ color: "#e9ecf3" }}>
            {item.year}
          </span>
        </div>

        {/* Watch Now Button */}
        <div className="mt-4 pointer-events-auto">
          <Link
            to={`/content/${item.id}`}
            onClick={scrollToTop}
            className="inline-flex items-center gap-2 px-6 py-3 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg font-semibold transition-colors shadow-lg hover:shadow-xl transform hover:scale-105 duration-200"
          >
            <Play className="w-5 h-5" />
            Watch Now
          </Link>
        </div>
      </div>
    </section>
  );
}

