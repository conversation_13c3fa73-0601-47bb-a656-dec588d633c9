
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import PaginatedGrid from "@/components/PaginatedGrid";
import { mediaData } from "@/data/movies";
import { Link } from "react-router-dom";
import { scrollToTop } from "@/utils/scrollToTop";

const movies = mediaData.filter(m => m.type === "movie");

export default function AllMovies() {
  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      <main className="flex-1 max-w-7xl w-full mx-auto px-3 sm:px-4 py-6 sm:py-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6 sm:mb-8">
          <h1
            className="stdb-heading"
            style={{
              color: "#e6cb8e",
              fontFamily: "'Koulen', Impact, Arial, sans-serif",
              fontWeight: 400,
              fontSize: "1.75rem",
              letterSpacing: "0.07em",
              margin: 0,
              textTransform: "uppercase",
              textShadow: "0 2px 16px #19191740, 0 1px 2px #0002"
            }}
          >
            All Movies
          </h1>
          <Link
            to="/"
            className="inline-block px-3 py-2 sm:py-1 rounded font-koulen uppercase text-xs sm:text-xs tracking-wide bg-[#e6cb8e] text-[#232323] hover:opacity-85 border border-[#e6cb8e] shadow transition min-h-[44px] flex items-center justify-center"
            style={{
              textShadow: "0 1px 4px #0003",
              fontWeight: 400,
              letterSpacing: "0.08em"
            }}
            aria-label="Back to Home"
            onClick={scrollToTop}
          >
            ← Back to Home
          </Link>
        </div>
        <PaginatedGrid items={movies} pageSize={8} />
      </main>
      <Footer />
    </div>
  );
}
