/**
 * Ad Blocker Awareness Popup Tracking System
 * 
 * This module handles session tracking for the ad blocker awareness popup,
 * including localStorage management and future database integration structure.
 */

// Constants
const STORAGE_KEY = 'adBlockerAwarenessShown';
const DISPLAY_INTERVAL_HOURS = 24;
const DISPLAY_INTERVAL_MS = DISPLAY_INTERVAL_HOURS * 60 * 60 * 1000; // 24 hours in milliseconds

// Types for future database integration
export interface AwarenessTrackingRecord {
  userId?: string; // For future database integration
  sessionId: string;
  lastShownTimestamp: number;
  dismissCount: number;
  userAgent: string;
  createdAt: number;
  updatedAt: number;
}

export interface AwarenessTrackingOptions {
  forceShow?: boolean; // For testing purposes
  customInterval?: number; // Custom interval in hours
  useDatabase?: boolean; // Future flag for database integration
}

/**
 * Generate a unique session ID for tracking
 */
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get current tracking record from localStorage
 */
function getLocalStorageRecord(): AwarenessTrackingRecord | null {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return null;
    
    const record = JSON.parse(stored) as AwarenessTrackingRecord;
    
    // Validate record structure
    if (typeof record.lastShownTimestamp !== 'number' || 
        typeof record.dismissCount !== 'number') {
      console.warn('Invalid awareness tracking record, resetting...');
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }
    
    return record;
  } catch (error) {
    console.error('Error reading awareness tracking record:', error);
    localStorage.removeItem(STORAGE_KEY);
    return null;
  }
}

/**
 * Save tracking record to localStorage
 */
function saveLocalStorageRecord(record: AwarenessTrackingRecord): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(record));
  } catch (error) {
    console.error('Error saving awareness tracking record:', error);
  }
}

/**
 * Create a new tracking record
 */
function createNewRecord(): AwarenessTrackingRecord {
  const now = Date.now();
  return {
    sessionId: generateSessionId(),
    lastShownTimestamp: 0, // Will be set when popup is shown
    dismissCount: 0,
    userAgent: navigator.userAgent,
    createdAt: now,
    updatedAt: now
  };
}

/**
 * Check if popup should be shown based on timing rules
 */
export function shouldShowAwarenessPopup(options: AwarenessTrackingOptions = {}): boolean {
  // Force show for testing
  if (options.forceShow) {
    return true;
  }

  const record = getLocalStorageRecord();
  
  // If no record exists, show popup (first visit)
  if (!record) {
    return true;
  }

  // Check if enough time has passed since last shown
  const now = Date.now();
  const intervalMs = options.customInterval 
    ? options.customInterval * 60 * 60 * 1000 
    : DISPLAY_INTERVAL_MS;
  
  const timeSinceLastShown = now - record.lastShownTimestamp;
  
  return timeSinceLastShown >= intervalMs;
}

/**
 * Record that the popup was shown
 */
export function recordPopupShown(): void {
  const now = Date.now();
  let record = getLocalStorageRecord();
  
  if (!record) {
    record = createNewRecord();
  }
  
  record.lastShownTimestamp = now;
  record.updatedAt = now;
  
  saveLocalStorageRecord(record);
  
  console.log('Ad blocker awareness popup shown and recorded');
}

/**
 * Record that the popup was dismissed by user
 */
export function recordPopupDismissed(): void {
  const now = Date.now();
  let record = getLocalStorageRecord();
  
  if (!record) {
    record = createNewRecord();
  }
  
  record.dismissCount += 1;
  record.updatedAt = now;
  
  saveLocalStorageRecord(record);
  
  console.log(`Ad blocker awareness popup dismissed (total: ${record.dismissCount})`);
}

/**
 * Get tracking statistics for analytics
 */
export function getTrackingStats(): {
  hasRecord: boolean;
  lastShown: Date | null;
  dismissCount: number;
  daysSinceLastShown: number;
  sessionId: string | null;
} {
  const record = getLocalStorageRecord();
  
  if (!record) {
    return {
      hasRecord: false,
      lastShown: null,
      dismissCount: 0,
      daysSinceLastShown: -1,
      sessionId: null
    };
  }
  
  const daysSinceLastShown = record.lastShownTimestamp > 0 
    ? Math.floor((Date.now() - record.lastShownTimestamp) / (1000 * 60 * 60 * 24))
    : -1;
  
  return {
    hasRecord: true,
    lastShown: record.lastShownTimestamp > 0 ? new Date(record.lastShownTimestamp) : null,
    dismissCount: record.dismissCount,
    daysSinceLastShown,
    sessionId: record.sessionId
  };
}

/**
 * Reset tracking data (for testing or user request)
 */
export function resetTrackingData(): void {
  localStorage.removeItem(STORAGE_KEY);
  console.log('Ad blocker awareness tracking data reset');
}

/**
 * Future database integration functions
 * These will be implemented when database connectivity is added
 */

/**
 * Save tracking record to database (future implementation)
 */
export async function saveToDatabase(record: AwarenessTrackingRecord): Promise<boolean> {
  // TODO: Implement database save when backend is connected
  console.log('Database save not yet implemented:', record);
  return false;
}

/**
 * Load tracking record from database (future implementation)
 */
export async function loadFromDatabase(userId: string): Promise<AwarenessTrackingRecord | null> {
  // TODO: Implement database load when backend is connected
  console.log('Database load not yet implemented for user:', userId);
  return null;
}

/**
 * Sync localStorage data with database (future implementation)
 */
export async function syncWithDatabase(userId: string): Promise<boolean> {
  try {
    const localRecord = getLocalStorageRecord();
    if (!localRecord) return false;
    
    // TODO: Implement sync logic when database is connected
    // 1. Load record from database
    // 2. Compare timestamps
    // 3. Merge data (keep most recent)
    // 4. Save merged record to both localStorage and database
    
    console.log('Database sync not yet implemented for user:', userId);
    return false;
  } catch (error) {
    console.error('Error syncing with database:', error);
    return false;
  }
}

/**
 * Migrate from localStorage to database (future implementation)
 */
export async function migrateToDatabase(userId: string): Promise<boolean> {
  try {
    const localRecord = getLocalStorageRecord();
    if (!localRecord) return true; // Nothing to migrate
    
    // TODO: Implement migration when database is connected
    // 1. Save local record to database with userId
    // 2. Verify save was successful
    // 3. Optionally clear localStorage (or keep as backup)
    
    console.log('Database migration not yet implemented for user:', userId);
    return false;
  } catch (error) {
    console.error('Error migrating to database:', error);
    return false;
  }
}

/**
 * Get next show time for user information
 */
export function getNextShowTime(): Date | null {
  const record = getLocalStorageRecord();
  if (!record || record.lastShownTimestamp === 0) {
    return null; // Will show immediately
  }
  
  return new Date(record.lastShownTimestamp + DISPLAY_INTERVAL_MS);
}

/**
 * Check if user has dismissed popup multiple times (for analytics)
 */
export function isFrequentDismisser(): boolean {
  const record = getLocalStorageRecord();
  return record ? record.dismissCount >= 3 : false;
}

/**
 * Development/testing utilities
 */
export const devUtils = {
  /**
   * Force show popup (for testing)
   */
  forceShow: () => shouldShowAwarenessPopup({ forceShow: true }),
  
  /**
   * Set custom interval (for testing)
   */
  setTestInterval: (hours: number) => shouldShowAwarenessPopup({ customInterval: hours }),
  
  /**
   * Get raw record data
   */
  getRawRecord: () => getLocalStorageRecord(),
  
  /**
   * Manually set last shown time
   */
  setLastShown: (timestamp: number) => {
    let record = getLocalStorageRecord() || createNewRecord();
    record.lastShownTimestamp = timestamp;
    record.updatedAt = Date.now();
    saveLocalStorageRecord(record);
  }
};
