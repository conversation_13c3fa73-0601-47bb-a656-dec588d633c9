import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';
import {
  Shield,
  ShieldAlert,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Heart,
  Server,
  Loader2,
  Zap,
  ExternalLink,
  X,
  Play
} from 'lucide-react';
import { AdBlockerInfo, AdBlockerDetectionResult, recheckAdBlockers, attemptOneClickDisable } from '@/utils/adBlockerDetection';

interface AdBlockerDetectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  detectionResult: AdBlockerDetectionResult;
  onRetry: () => void;
  onProceed: () => void;
  onDismissAndProceed?: () => void; // New prop for dismissing and allowing video playback
}

const AdBlockerDetectionModal: React.FC<AdBlockerDetectionModalProps> = ({
  isOpen,
  onClose,
  detectionResult,
  onRetry,
  onProceed,
  onDismissAndProceed
}) => {
  const [isRechecking, setIsRechecking] = useState(false);
  const [recheckResult, setRecheckResult] = useState<boolean | null>(null);
  const [expandedBlocker, setExpandedBlocker] = useState<string | null>(null);
  const [isOneClickDisabling, setIsOneClickDisabling] = useState(false);
  const [oneClickResult, setOneClickResult] = useState<string | null>(null);

  const handleRecheck = async () => {
    setIsRechecking(true);
    setRecheckResult(null);
    
    try {
      const isClean = await recheckAdBlockers();
      setRecheckResult(isClean);
      
      if (isClean) {
        // Wait a moment to show success, then proceed
        setTimeout(() => {
          onProceed();
        }, 1500);
      }
    } catch (error) {
      console.error('Recheck failed:', error);
      setRecheckResult(false);
    } finally {
      setIsRechecking(false);
    }
  };

  const handleOneClickDisable = async () => {
    setIsOneClickDisabling(true);
    setOneClickResult(null);
    
    try {
      // Attempt to guide user through automatic disable process
      const result = await attemptOneClickDisable(detectionResult.detectedBlockers);
      setOneClickResult(result);
      
      // After showing guidance, automatically recheck
      setTimeout(async () => {
        const isClean = await recheckAdBlockers();
        if (isClean) {
          setRecheckResult(true);
          setTimeout(() => {
            onProceed();
          }, 1500);
        } else {
          setRecheckResult(false);
        }
      }, 3000);
      
    } catch (error) {
      console.error('One-click disable failed:', error);
      setOneClickResult('Unable to automatically disable ad blockers. Please follow the manual instructions below.');
    } finally {
      setIsOneClickDisabling(false);
    }
  };

  const getBlockerTypeIcon = (type: AdBlockerInfo['type']) => {
    switch (type) {
      case 'extension':
        return <Shield className="w-4 h-4" />;
      case 'browser':
        return <ShieldAlert className="w-4 h-4" />;
      case 'antivirus':
        return <Server className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const getBlockerTypeBadgeVariant = (type: AdBlockerInfo['type']) => {
    switch (type) {
      case 'extension':
        return 'default';
      case 'browser':
        return 'secondary';
      case 'antivirus':
        return 'outline';
      default:
        return 'destructive';
    }
  };

  const toggleBlockerExpansion = (blockerName: string) => {
    setExpandedBlocker(expandedBlocker === blockerName ? null : blockerName);
  };

  if (!isOpen) return null;

  // Render modal using portal to escape any parent container constraints
  return createPortal(
    <>
      {/* Subtle backdrop */}
      <div className="ad-blocker-modal-backdrop-global fixed inset-0 bg-black/50 backdrop-blur-sm" />

      {/* Non-blocking popup */}
      <div className="ad-blocker-modal-global fixed inset-0 pointer-events-none">
        <div className="flex items-start justify-center pt-8 sm:pt-12 md:pt-16 px-4 h-full overflow-y-auto">
          <Card className="ad-blocker-modal-content relative max-w-2xl w-[95vw] sm:w-full pointer-events-auto bg-card border-border shadow-2xl">
            {/* Prominent Close Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismissAndProceed || onClose}
              className="absolute right-3 top-3 h-10 w-10 p-0 hover:bg-primary/10 text-muted-foreground hover:text-foreground transition-colors z-10"
              aria-label="Close and continue watching"
            >
              <X className="h-5 w-5" />
            </Button>

            <CardContent className="p-6 pr-16">
              {/* Header */}
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 rounded-full bg-orange-500/20">
                  {/* Filled Shield Alert Icon */}
                  <svg className="w-6 h-6 text-orange-500 fill-current" viewBox="0 0 24 24">
                    <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.11,7 14,7.89 14,9C14,10.11 13.11,11 12,11C10.89,11 10,10.11 10,9C10,7.89 10.89,7 12,7M12,14C13.66,14 15,14.67 15,15.5V16.5H9V15.5C9,14.67 10.34,14 12,14Z"/>
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-foreground">
                    Ad Blocker Detected
                  </h2>
                  <p className="text-muted-foreground">
                    You can still watch - this is just a friendly notice
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                {/* Polite Message */}
                <Alert className="border-primary/20 bg-primary/5">
                  {/* Filled Heart Icon */}
                  <svg className="w-4 h-4 text-red-500 fill-current" viewBox="0 0 24 24">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                  </svg>
                  <AlertDescription className="text-sm">
                    <div className="space-y-2">
                      <p className="font-medium text-foreground">
                        We understand ads can be annoying, but they help us keep this service free!
                      </p>
                      <p className="text-muted-foreground">
                        Our ads help cover server costs and storage expenses so we can continue providing 
                        free streaming content. Please consider disabling your ad blocker for this site.
                      </p>
                      <p className="text-xs text-green-600 font-medium">
                        ✨ You can close this popup and continue watching - no restrictions!
                      </p>
                    </div>
                  </AlertDescription>
                </Alert>

                {/* Detection Results */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-foreground">
                      Detected Ad Blockers ({detectionResult.detectedBlockers.length})
                    </h3>
                    <Badge variant="outline" className="text-xs">
                      {Math.round(detectionResult.totalConfidence)}% confidence
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    {detectionResult.detectedBlockers.map((blocker, index) => (
                      <div
                        key={index}
                        className="border border-border rounded-lg p-3 sm:p-4 bg-card/50 hover:bg-card/80 transition-colors"
                      >
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                          <div className="flex items-center gap-3 min-w-0 flex-1">
                            {getBlockerTypeIcon(blocker.type)}
                            <div className="min-w-0 flex-1">
                              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                                <span className="font-medium text-foreground text-sm sm:text-base truncate">{blocker.name}</span>
                                <Badge
                                  variant={getBlockerTypeBadgeVariant(blocker.type)}
                                  className="text-xs w-fit"
                                >
                                  {blocker.type}
                                </Badge>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1 sm:mt-0">
                                <span className="hidden sm:inline">Detected via: </span>{blocker.detectionMethod}
                              </p>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleBlockerExpansion(blocker.name)}
                            className="text-primary hover:text-primary/80 text-xs sm:text-sm self-start sm:self-center"
                          >
                            <span className="hidden sm:inline">{expandedBlocker === blocker.name ? 'Hide' : 'Show'} Instructions</span>
                            <span className="sm:hidden">{expandedBlocker === blocker.name ? 'Hide' : 'Show'}</span>
                          </Button>
                        </div>

                        {expandedBlocker === blocker.name && (
                          <div className="mt-4 space-y-4 border-t border-border pt-4">
                            {/* Disable Instructions */}
                            <div>
                              <h4 className="font-medium text-foreground mb-2 flex items-center gap-2">
                                <AlertTriangle className="w-4 h-4 text-orange-500" />
                                How to Disable
                              </h4>
                              <ol className="space-y-1 text-sm text-muted-foreground">
                                {blocker.disableInstructions.map((instruction, idx) => (
                                  <li key={idx} className="flex gap-2">
                                    <span className="text-primary font-medium">{idx + 1}.</span>
                                    <span>{instruction}</span>
                                  </li>
                                ))}
                              </ol>
                            </div>

                            {/* Whitelist Instructions */}
                            <div>
                              <h4 className="font-medium text-foreground mb-2 flex items-center gap-2">
                                <CheckCircle className="w-4 h-4 text-green-500" />
                                How to Whitelist This Site
                              </h4>
                              <ol className="space-y-1 text-sm text-muted-foreground">
                                {blocker.whitelistInstructions.map((instruction, idx) => (
                                  <li key={idx} className="flex gap-2">
                                    <span className="text-primary font-medium">{idx + 1}.</span>
                                    <span>{instruction}</span>
                                  </li>
                                ))}
                              </ol>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* One-Click Disable Guide */}
                <div className="border-t border-border pt-6">
                  <h3 className="text-lg font-medium text-foreground mb-3">
                    Ready to Continue?
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Use the one-click disable button below for quick guidance, or manually disable your ad blocker and verify.
                  </p>

                  {/* One-Click Disable Button */}
                  <div className="mb-4">
                    <Button
                      onClick={handleOneClickDisable}
                      disabled={isOneClickDisabling}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium min-h-[48px] text-sm sm:text-base"
                    >
                      {isOneClickDisabling ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          <span className="hidden sm:inline">Providing Guidance...</span>
                          <span className="sm:hidden">Loading...</span>
                        </>
                      ) : (
                        <>
                          <Zap className="w-4 h-4 mr-2" />
                          <span className="hidden sm:inline">One-Click Disable Guide</span>
                          <span className="sm:hidden">Quick Disable</span>
                        </>
                      )}
                    </Button>

                    {/* One-Click Result */}
                    {oneClickResult && (
                      <Alert className="mt-3 border-blue-500/20 bg-blue-500/5">
                        <ExternalLink className="w-4 h-4 text-blue-500" />
                        <AlertDescription className="text-blue-700 dark:text-blue-400">
                          {oneClickResult}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={handleRecheck}
                      disabled={isRechecking}
                      className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px] text-sm sm:text-base"
                    >
                      {isRechecking ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          <span className="hidden sm:inline">Checking...</span>
                          <span className="sm:hidden">Checking</span>
                        </>
                      ) : (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2" />
                          <span className="hidden sm:inline">I've Disabled My Ad Blocker</span>
                          <span className="sm:hidden">Disabled</span>
                        </>
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      onClick={onRetry}
                      className="flex-1 min-h-[44px] text-sm sm:text-base"
                    >
                      <span className="hidden sm:inline">Retry Detection</span>
                      <span className="sm:hidden">Retry</span>
                    </Button>
                  </div>

                  {/* Recheck Result */}
                  {recheckResult !== null && (
                    <div className="mt-4">
                      {recheckResult ? (
                        <Alert className="border-green-500/20 bg-green-500/5">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <AlertDescription className="text-green-700 dark:text-green-400">
                            Great! No ad blockers detected. Redirecting to video...
                          </AlertDescription>
                        </Alert>
                      ) : (
                        <Alert className="border-orange-500/20 bg-orange-500/5">
                          <AlertTriangle className="w-4 h-4 text-orange-500" />
                          <AlertDescription className="text-orange-700 dark:text-orange-400">
                            Ad blocker still detected. Please make sure you've disabled it completely and refreshed the page.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}
                </div>

                {/* Continue Watching Button */}
                <div className="border-t border-border pt-6">
                  <Button
                    onClick={onDismissAndProceed || onClose}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium min-h-[48px] text-base"
                  >
                    <Play className="w-5 h-5 mr-2" />
                    Continue Watching Anyway
                  </Button>
                  <p className="text-xs text-center text-muted-foreground mt-2">
                    You can watch the video even with ad blockers enabled
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>,
    document.body
  );
};

export default AdBlockerDetectionModal;
