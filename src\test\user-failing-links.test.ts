/**
 * Test file specifically for the user's failing embed links
 * Tests the three specific links that were failing validation
 */

import { 
  isValidVideoLink, 
  isSecureVideoLink, 
  extractVideoUrl, 
  detectVideoPlatform 
} from '../utils/videoSecurity';

// The three specific failing embed links from the user
const userFailingLinks = [
  'https://gradehgplus.com/e/xvay1ggua7s7',
  'https://streamtape.com/v/YeRw6amy3MsvWa7/Dont.Leave.2022.720p.WEB-DL.English.ESubs.MoviesMod.com.mkv',
  'https://filemoon.to/e/ezfjmgsjwwsh'
];

/**
 * Test the specific failing links that the user reported
 */
export function testUserFailingLinks(): boolean {
  console.group('🔧 Testing User\'s Previously Failing Links');
  
  let allPassed = true;
  
  userFailingLinks.forEach((link, index) => {
    console.group(`Testing Link ${index + 1}: ${link.substring(0, 50)}...`);
    
    const isValid = isValidVideoLink(link);
    const isSecure = isSecureVideoLink(link);
    const extractedUrl = extractVideoUrl(link);
    const platform = detectVideoPlatform(link);
    
    console.log(`Original URL: ${link}`);
    console.log(`Extracted URL: ${extractedUrl}`);
    console.log(`Platform: ${platform}`);
    console.log(`Valid: ${isValid}`);
    console.log(`Secure: ${isSecure}`);
    
    const linkPassed = isValid && isSecure;
    
    if (linkPassed) {
      console.log(`✅ PASS - Link ${index + 1} now validates successfully`);
    } else {
      console.error(`❌ FAIL - Link ${index + 1} still failing validation`);
      console.error(`  Valid: ${isValid}, Secure: ${isSecure}`);
      allPassed = false;
    }
    
    console.groupEnd();
  });
  
  console.log(`\n📊 Result: ${allPassed ? 'ALL LINKS PASS' : 'SOME LINKS STILL FAILING'}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Test platform detection for the new platforms
 */
export function testNewPlatformDetection(): boolean {
  console.group('🎯 Testing New Platform Detection');
  
  const platformTests = [
    { url: 'https://gradehgplus.com/e/xvay1ggua7s7', expected: 'gradehgplus' },
    { url: 'https://streamtape.com/v/YeRw6amy3MsvWa7/video.mkv', expected: 'streamtape' },
    { url: 'https://filemoon.to/e/ezfjmgsjwwsh', expected: 'filemoon' },
    { url: 'https://filemoon.sx/e/test123', expected: 'filemoon' },
    { url: 'https://filemoon.in/e/test456', expected: 'filemoon' }
  ];
  
  let allPassed = true;
  
  platformTests.forEach((test, index) => {
    const detected = detectVideoPlatform(test.url);
    const passed = detected === test.expected;
    
    if (passed) {
      console.log(`✅ Test ${index + 1}: ${test.expected} detected correctly`);
    } else {
      console.error(`❌ Test ${index + 1}: Expected '${test.expected}', got '${detected}'`);
      allPassed = false;
    }
  });
  
  console.log(`\n📊 Platform Detection: ${allPassed ? 'ALL PASS' : 'SOME FAILED'}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Test various URL patterns for the new platforms
 */
export function testNewPlatformPatterns(): boolean {
  console.group('🔍 Testing New Platform URL Patterns');
  
  const patternTests = [
    // gradehgplus.com patterns
    'https://gradehgplus.com/e/abc123',
    '//gradehgplus.com/e/def456',
    
    // streamtape.com patterns
    'https://streamtape.com/v/ghi789/video.mp4',
    'https://streamtape.com/e/jkl012',
    '//streamtape.com/v/mno345/test.mkv',
    
    // filemoon patterns
    'https://filemoon.to/e/pqr678',
    'https://filemoon.sx/e/stu901',
    'https://filemoon.in/e/vwx234',
    '//filemoon.to/e/yza567'
  ];
  
  let allPassed = true;
  
  patternTests.forEach((url, index) => {
    const isValid = isValidVideoLink(url);
    const isSecure = isSecureVideoLink(url);
    const passed = isValid && isSecure;
    
    if (passed) {
      console.log(`✅ Pattern ${index + 1}: ${url.substring(0, 40)}... - PASS`);
    } else {
      console.error(`❌ Pattern ${index + 1}: ${url.substring(0, 40)}... - FAIL`);
      console.error(`  Valid: ${isValid}, Secure: ${isSecure}`);
      allPassed = false;
    }
  });
  
  console.log(`\n📊 Pattern Tests: ${allPassed ? 'ALL PASS' : 'SOME FAILED'}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Run all tests for the user's failing links fix
 */
export function runUserFailingLinksTests(): boolean {
  console.group('🧪 Running User Failing Links Fix Tests');
  
  const results = [
    testUserFailingLinks(),
    testNewPlatformDetection(),
    testNewPlatformPatterns()
  ];
  
  const allPassed = results.every(Boolean);
  const passedCount = results.filter(Boolean).length;
  
  console.log(`\n🎯 Overall Result: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  console.log(`📊 Summary: ${passedCount}/${results.length} test suites passed`);
  
  if (allPassed) {
    console.log('🎉 The user\'s failing embed links should now work correctly!');
  } else {
    console.error('⚠️ Some issues remain - check the failed tests above');
  }
  
  console.groupEnd();
  
  return allPassed;
}

// Export the failing links for use in other tests
export { userFailingLinks };
