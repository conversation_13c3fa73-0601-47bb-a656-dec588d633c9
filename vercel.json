{"buildCommand": "npm run build", "outputDirectory": "dist", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pagead2.googlesyndication.com https://www.googletagservices.com https://securepubads.g.doubleclick.net https://googleads.g.doubleclick.net https://tpc.googlesyndication.com https://httpbin.org; connect-src 'self' https://pagead2.googlesyndication.com https://www.googletagservices.com https://securepubads.g.doubleclick.net https://googleads.g.doubleclick.net https://tpc.googlesyndication.com https://httpbin.org https://api.themoviedb.org; img-src 'self' data: https: http:; style-src 'self' 'unsafe-inline'; font-src 'self' data:; frame-src 'self' https:; object-src 'none'; base-uri 'self';"}]}]}