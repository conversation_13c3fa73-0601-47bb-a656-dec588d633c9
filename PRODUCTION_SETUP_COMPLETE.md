# 🚀 StreamDB Online - Complete Production Setup Guide

## 📋 OVERVIEW

This guide provides step-by-step instructions for deploying StreamDB Online to your Alexhost VPS with complete security, auto-deployment, and production-ready configuration.

## ✅ PREREQUISITES CHECKLIST

- [ ] Alexhost VPS with FastPanel access
- [ ] Domain name (streamdb.online) pointed to your server
- [ ] SSH access to your server
- [ ] MySQL database created in FastPanel
- [ ] Node.js 18+ installed
- [ ] Git installed

## 🔧 PHASE 1: SERVER PREPARATION

### Step 1.1: Connect to Your Server
```bash
# SSH into your Alexhost server
ssh streamdb_onl_usr@your-server-ip
```

### Step 1.2: Install Required Tools
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18 (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Verify installations
node --version  # Should be v18+
npm --version
pm2 --version
```

### Step 1.3: Set Up Project Directory
```bash
# Navigate to your web directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Clone your repository (replace with your actual repo URL)
git clone https://github.com/yourusername/your-repo.git .

# Set proper ownership
sudo chown -R streamdb_onl_usr:streamdb_onl_usr .
```

## 🔐 PHASE 2: SECURITY SETUP

### Step 2.1: Generate Production Environment
```bash
# Navigate to server directory
cd server

# Run the production setup script
node setup-production.js
```

**Follow the prompts to configure:**
- Database credentials (from FastPanel)
- Admin user credentials
- Domain configuration
- Security keys (auto-generated)

### Step 2.2: Secure File Permissions
```bash
# Set secure permissions on environment file
chmod 600 server/.env

# Create secure upload directories
mkdir -p uploads/{images,videos,subtitles,temp}
chmod 755 uploads
chmod 777 uploads/temp

# Create logs directory
mkdir -p logs
chmod 755 logs
```

### Step 2.3: Run Security Audit
```bash
# Run comprehensive security audit
cd server
node security-audit.js
```

**Fix any critical issues before proceeding!**

## 💾 PHASE 3: DATABASE SETUP

### Step 3.1: Create Database Schema
1. **Open FastPanel → MySQL → phpMyAdmin**
2. **Select your database**
3. **Import the schema:**
   ```sql
   -- Copy and paste contents of database/schema.sql
   ```

### Step 3.2: Create Admin User
```bash
# Navigate to server directory
cd server

# Create secure admin user in database
node -e "
const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function createAdmin() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    socketPath: process.env.DB_SOCKET,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });
  
  const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD, 12);
  
  await connection.execute(
    'INSERT INTO admin_users (username, email, password, role, is_active) VALUES (?, ?, ?, ?, ?)',
    [process.env.ADMIN_USERNAME, process.env.ADMIN_EMAIL, hashedPassword, 'admin', 1]
  );
  
  console.log('✅ Admin user created successfully');
  await connection.end();
}

createAdmin().catch(console.error);
"
```

## 🏗️ PHASE 4: APPLICATION DEPLOYMENT

### Step 4.1: Install Dependencies
```bash
# Install frontend dependencies
npm install

# Install server dependencies
cd server
npm install --production
cd ..
```

### Step 4.2: Build Frontend
```bash
# Build production frontend
npm run build
```

### Step 4.3: Start Services with PM2
```bash
# Start the main application
cd server
pm2 start index.js --name streamdb-online --env production

# Save PM2 configuration
pm2 save

# Set up PM2 to start on boot
pm2 startup
# Follow the instructions provided by PM2
```

## 🔄 PHASE 5: AUTO-DEPLOYMENT SETUP

### Step 5.1: Configure Deployment Scripts
```bash
# Make deployment script executable
chmod +x deployment/deploy.sh

# Set up deployment environment
./deployment/deploy.sh setup
```

### Step 5.2: Start Webhook Handler
```bash
# Start webhook handler for GitHub auto-deployment
pm2 start deployment/webhook-handler.js --name webhook-handler

# Save PM2 configuration
pm2 save
```

### Step 5.3: Configure GitHub Webhook
1. **Go to your GitHub repository**
2. **Settings → Webhooks → Add webhook**
3. **Configure:**
   - **Payload URL:** `https://streamdb.online:9000/webhook`
   - **Content type:** `application/json`
   - **Secret:** Use `WEBHOOK_SECRET` from your `.env` file
   - **Events:** Just the push event
   - **Active:** ✅ Checked

## 🌐 PHASE 6: WEB SERVER CONFIGURATION

### Step 6.1: Nginx Configuration (if using Nginx)
```nginx
# Add to your Nginx site configuration
server {
    listen 80;
    listen 443 ssl;
    server_name streamdb.online www.streamdb.online;
    
    # SSL configuration (if using SSL)
    # ssl_certificate /path/to/certificate.crt;
    # ssl_certificate_key /path/to/private.key;
    
    # Serve static files
    location / {
        root /var/www/streamdb_onl_usr/data/www/streamdb.online/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Webhook endpoint
    location /webhook {
        proxy_pass http://localhost:9000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Upload files
    location /uploads/ {
        root /var/www/streamdb_onl_usr/data/www/streamdb.online;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### Step 6.2: Test and Reload Nginx
```bash
# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

## ✅ PHASE 7: VERIFICATION & TESTING

### Step 7.1: Test Application
```bash
# Check PM2 status
pm2 status

# Check application logs
pm2 logs streamdb-online

# Test API health
curl http://localhost:3001/api/health

# Test webhook handler
curl http://localhost:9000/health
```

### Step 7.2: Test Auto-Deployment
1. **Make a small change to your code**
2. **Commit and push to main branch:**
   ```bash
   git add .
   git commit -m "Test auto-deployment"
   git push origin main
   ```
3. **Check deployment logs:**
   ```bash
   tail -f /var/log/streamdb-deploy.log
   pm2 logs webhook-handler
   ```

### Step 7.3: Access Admin Panel
1. **Visit:** `https://streamdb.online/admin`
2. **Login with credentials from setup:**
   - Username: From `ADMIN_USERNAME` in `.env`
   - Password: From `ADMIN_PASSWORD` in `.env`

## 🔒 SECURITY CHECKLIST

- [ ] `.env` file has 600 permissions
- [ ] No credentials visible in browser source code
- [ ] HTTPS enabled (recommended)
- [ ] Database using socket connection
- [ ] Strong admin password generated
- [ ] Security audit passed
- [ ] Rate limiting configured
- [ ] File upload restrictions in place
- [ ] Webhook signature verification enabled

## 🛠️ MAINTENANCE COMMANDS

```bash
# View application status
pm2 status

# View logs
pm2 logs streamdb-online
tail -f /var/log/streamdb-deploy.log

# Manual deployment
./deployment/deploy.sh deploy

# Rollback deployment
./deployment/deploy.sh rollback

# Restart services
pm2 restart all

# Run security audit
cd server && node security-audit.js

# Update dependencies
npm update && cd server && npm update
```

## 🚨 TROUBLESHOOTING

### Common Issues:

1. **Database Connection Failed**
   - Check MySQL socket path: `/var/run/mysqld/mysqld.sock`
   - Verify database credentials in `.env`
   - Ensure database exists in FastPanel

2. **PM2 Process Not Starting**
   - Check Node.js version: `node --version`
   - Verify dependencies: `npm install`
   - Check logs: `pm2 logs streamdb-online`

3. **Webhook Not Working**
   - Verify webhook secret matches GitHub
   - Check firewall allows port 9000
   - Test webhook handler: `curl http://localhost:9000/health`

4. **Frontend Not Loading**
   - Ensure build completed: `npm run build`
   - Check Nginx configuration
   - Verify static files in `dist/` directory

## 📞 SUPPORT

If you encounter issues:
1. Check the logs: `pm2 logs` and `/var/log/streamdb-deploy.log`
2. Run security audit: `node server/security-audit.js`
3. Verify all prerequisites are met
4. Check file permissions and ownership

## 🎉 SUCCESS!

Once all steps are completed, your StreamDB Online website will be:
- ✅ Fully deployed and running
- ✅ Secured with proper authentication
- ✅ Auto-deploying from GitHub pushes
- ✅ Backed up automatically
- ✅ Monitored with PM2
- ✅ Production-ready and scalable

**Your website is now live at: https://streamdb.online**
