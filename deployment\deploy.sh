#!/bin/bash

# StreamDB Online - Production Deployment Script
# Secure auto-deployment with backup and rollback capabilities
# Usage: ./deploy.sh [deploy|rollback|status]

set -e  # Exit on any error

# Configuration
PROJECT_NAME="streamdb-online"
PROJECT_DIR="/var/www/streamdb_onl_usr/data/www/streamdb.online"
BACKUP_DIR="/var/backups/streamdb-online"
LOG_FILE="/var/log/streamdb-deploy.log"
REPO_URL="https://github.com/yourusername/your-repo.git"
BRANCH="main"
NODE_VERSION="18"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages with timestamp
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log_message "INFO" "$1"
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    log_message "SUCCESS" "$1"
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    log_message "WARNING" "$1"
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    log_message "ERROR" "$1"
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if running as correct user
    if [ "$USER" != "streamdb_onl_usr" ] && [ "$USER" != "root" ]; then
        log_warning "Not running as streamdb_onl_usr or root user"
    fi
    
    # Check required commands
    local required_commands=("git" "node" "npm" "pm2")
    for cmd in "${required_commands[@]}"; do
        if command_exists "$cmd"; then
            log_success "$cmd is installed"
        else
            log_error "$cmd is not installed"
            exit 1
        fi
    done
    
    # Check Node.js version
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -ge "$NODE_VERSION" ]; then
        log_success "Node.js version is adequate (v$(node --version))"
    else
        log_warning "Node.js version might be outdated (v$(node --version))"
    fi
    
    # Check if project directory exists
    if [ -d "$PROJECT_DIR" ]; then
        log_success "Project directory exists"
    else
        log_error "Project directory does not exist: $PROJECT_DIR"
        exit 1
    fi
}

# Function to create backup
create_backup() {
    log_info "Creating backup..."
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Generate backup name with timestamp
    local backup_name="backup-$(date +%Y%m%d-%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    # Create backup
    if cp -r "$PROJECT_DIR" "$backup_path"; then
        log_success "Backup created: $backup_path"
        
        # Keep only last 5 backups
        cd "$BACKUP_DIR"
        ls -t | tail -n +6 | xargs -r rm -rf
        log_info "Cleaned old backups (keeping last 5)"
        
        echo "$backup_name" > "$BACKUP_DIR/.latest"
    else
        log_error "Failed to create backup"
        exit 1
    fi
}

# Function to deploy application
deploy_application() {
    log_info "Starting deployment..."
    
    cd "$PROJECT_DIR"
    
    # Stash any local changes
    if git status --porcelain | grep -q .; then
        log_warning "Local changes detected, stashing..."
        git stash push -m "Auto-stash before deployment $(date)"
    fi
    
    # Fetch latest changes
    log_info "Fetching latest changes from GitHub..."
    git fetch origin "$BRANCH"
    
    # Reset to latest commit
    log_info "Updating to latest commit..."
    git reset --hard "origin/$BRANCH"
    
    # Install/update dependencies
    if [ -f "package.json" ]; then
        log_info "Installing frontend dependencies..."
        npm ci --production=false
    fi
    
    if [ -f "server/package.json" ]; then
        log_info "Installing server dependencies..."
        cd server
        npm ci --production
        cd ..
    fi
    
    # Build frontend
    if [ -f "package.json" ] && grep -q '"build"' package.json; then
        log_info "Building frontend..."
        npm run build
    fi
    
    # Run security audit
    if [ -f "server/security-audit.js" ]; then
        log_info "Running security audit..."
        cd server
        if node security-audit.js; then
            log_success "Security audit passed"
        else
            log_error "Security audit failed - deployment aborted"
            exit 1
        fi
        cd ..
    fi
    
    # Restart services
    log_info "Restarting services..."
    
    # Restart Node.js application with PM2
    if pm2 list | grep -q "$PROJECT_NAME"; then
        pm2 restart "$PROJECT_NAME"
        log_success "Restarted $PROJECT_NAME with PM2"
    else
        cd server
        pm2 start index.js --name "$PROJECT_NAME" --env production
        log_success "Started $PROJECT_NAME with PM2"
        cd ..
    fi
    
    # Restart webhook handler if it exists
    if pm2 list | grep -q "webhook-handler"; then
        pm2 restart webhook-handler
        log_success "Restarted webhook handler"
    fi
    
    # Restart web server if needed
    if command_exists nginx && systemctl is-active --quiet nginx; then
        sudo systemctl reload nginx
        log_success "Reloaded Nginx"
    fi
    
    # Save PM2 configuration
    pm2 save
    
    log_success "Deployment completed successfully!"
}

# Function to rollback to previous version
rollback_application() {
    log_info "Starting rollback..."
    
    if [ ! -f "$BACKUP_DIR/.latest" ]; then
        log_error "No backup information found"
        exit 1
    fi
    
    local latest_backup=$(cat "$BACKUP_DIR/.latest")
    local backup_path="$BACKUP_DIR/$latest_backup"
    
    if [ ! -d "$backup_path" ]; then
        log_error "Backup directory not found: $backup_path"
        exit 1
    fi
    
    log_info "Rolling back to: $latest_backup"
    
    # Stop services
    pm2 stop "$PROJECT_NAME" || true
    
    # Remove current deployment
    rm -rf "$PROJECT_DIR"
    
    # Restore from backup
    cp -r "$backup_path" "$PROJECT_DIR"
    
    # Restart services
    cd "$PROJECT_DIR/server"
    pm2 start index.js --name "$PROJECT_NAME" --env production || pm2 restart "$PROJECT_NAME"
    
    log_success "Rollback completed successfully!"
}

# Function to show deployment status
show_status() {
    log_info "Deployment Status"
    echo "=================="
    
    # Git status
    if [ -d "$PROJECT_DIR/.git" ]; then
        cd "$PROJECT_DIR"
        echo "📂 Project Directory: $PROJECT_DIR"
        echo "🌿 Current Branch: $(git branch --show-current)"
        echo "📝 Latest Commit: $(git log -1 --oneline)"
        echo "📅 Last Update: $(git log -1 --format=%cd)"
    fi
    
    # PM2 status
    echo ""
    echo "🔄 PM2 Processes:"
    pm2 list
    
    # Disk usage
    echo ""
    echo "💾 Disk Usage:"
    df -h "$PROJECT_DIR" | tail -1
    
    # Recent backups
    echo ""
    echo "💼 Recent Backups:"
    if [ -d "$BACKUP_DIR" ]; then
        ls -la "$BACKUP_DIR" | tail -5
    else
        echo "No backups found"
    fi
    
    # Log tail
    echo ""
    echo "📋 Recent Deployment Logs:"
    if [ -f "$LOG_FILE" ]; then
        tail -10 "$LOG_FILE"
    else
        echo "No deployment logs found"
    fi
}

# Function to setup deployment environment
setup_deployment() {
    log_info "Setting up deployment environment..."
    
    # Create necessary directories
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Set up log rotation
    if [ ! -f "/etc/logrotate.d/streamdb-deploy" ]; then
        sudo tee /etc/logrotate.d/streamdb-deploy > /dev/null <<EOF
$LOG_FILE {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
}
EOF
        log_success "Set up log rotation"
    fi
    
    # Install PM2 startup script
    pm2 startup
    
    log_success "Deployment environment setup completed"
}

# Main execution
case "$1" in
    "deploy")
        check_prerequisites
        create_backup
        deploy_application
        ;;
    "rollback")
        check_prerequisites
        rollback_application
        ;;
    "status")
        show_status
        ;;
    "setup")
        setup_deployment
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|status|setup}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy latest version from GitHub"
        echo "  rollback - Rollback to previous version"
        echo "  status   - Show deployment status"
        echo "  setup    - Setup deployment environment"
        exit 1
        ;;
esac
