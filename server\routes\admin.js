const express = require('express');
const { body, validationResult, query } = require('express-validator');
const csvParser = require('csv-parser');
const csvWriter = require('csv-writer');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const db = require('../config/database');
const { authenticateToken, requireModerator, requireAdmin, logAdminAction } = require('../middleware/auth');

const router = express.Router();

// Get dashboard statistics
router.get('/dashboard', authenticateToken, requireModerator, async (req, res) => {
  try {
    // Get content statistics
    const contentStats = await db.getContentStats();
    
    // Get recent content
    const recentContent = await db.execute(`
      SELECT id, title, type, created_at, is_published, is_featured
      FROM content 
      ORDER BY created_at DESC 
      LIMIT 10
    `);

    // Get user statistics
    const [userStats] = await db.execute(`
      SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_users,
        SUM(CASE WHEN role = 'moderator' THEN 1 ELSE 0 END) as moderator_users
      FROM admin_users
    `);

    // Get category statistics
    const categoryStats = await db.execute(`
      SELECT 
        c.name,
        c.type,
        COUNT(content.id) as content_count
      FROM categories c
      LEFT JOIN content ON c.id = content.category_id
      WHERE c.is_active = 1
      GROUP BY c.id, c.name, c.type
      ORDER BY content_count DESC
    `);

    // Get recent security logs
    const recentLogs = await db.execute(`
      SELECT 
        sl.action,
        sl.ip_address,
        sl.created_at,
        u.username
      FROM admin_security_logs sl
      LEFT JOIN admin_users u ON sl.user_id = u.id
      ORDER BY sl.created_at DESC
      LIMIT 20
    `);

    res.json({
      success: true,
      data: {
        contentStats,
        userStats,
        categoryStats,
        recentContent,
        recentLogs
      }
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch dashboard data'
    });
  }
});

// Get all users (admin only)
router.get('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const users = await db.execute(`
      SELECT 
        id, username, email, role, is_active, last_login, 
        failed_login_attempts, locked_until, created_at, updated_at
      FROM admin_users
      ORDER BY created_at DESC
    `);

    res.json({
      success: true,
      data: users
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch users'
    });
  }
});

// Update user status (admin only)
router.patch('/users/:id/status', authenticateToken, requireAdmin, [
  body('is_active').isBoolean().withMessage('Active status must be boolean')
], logAdminAction('user_status_update'), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const userId = req.params.id;
    const { is_active } = req.body;

    // Check if user exists
    const [user] = await db.execute('SELECT username FROM admin_users WHERE id = ?', [userId]);
    if (!user) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    // Prevent deactivating yourself
    if (userId == req.user.userId && !is_active) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'You cannot deactivate your own account'
      });
    }

    // Update user status
    await db.execute(
      'UPDATE admin_users SET is_active = ?, updated_at = NOW() WHERE id = ?',
      [is_active, userId]
    );

    res.json({
      success: true,
      message: `User ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: {
        id: userId,
        username: user.username,
        is_active
      }
    });

  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update user status'
    });
  }
});

// Get security logs
router.get('/security-logs', authenticateToken, requireAdmin, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('action').optional().isString().withMessage('Action must be a string'),
  query('user_id').optional().isInt().withMessage('User ID must be an integer')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        sl.id, sl.action, sl.ip_address, sl.user_agent, sl.details, sl.created_at,
        u.username, u.role
      FROM admin_security_logs sl
      LEFT JOIN admin_users u ON sl.user_id = u.id
      WHERE 1=1
    `;
    const params = [];

    if (req.query.action) {
      query += ' AND sl.action = ?';
      params.push(req.query.action);
    }

    if (req.query.user_id) {
      query += ' AND sl.user_id = ?';
      params.push(req.query.user_id);
    }

    query += ' ORDER BY sl.created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    const logs = await db.execute(query, params);

    // Get total count
    let countQuery = 'SELECT COUNT(*) as count FROM admin_security_logs sl WHERE 1=1';
    const countParams = [];

    if (req.query.action) {
      countQuery += ' AND sl.action = ?';
      countParams.push(req.query.action);
    }

    if (req.query.user_id) {
      countQuery += ' AND sl.user_id = ?';
      countParams.push(req.query.user_id);
    }

    const [totalCount] = await db.execute(countQuery, countParams);
    const totalPages = Math.ceil(totalCount.count / limit);

    res.json({
      success: true,
      data: logs,
      pagination: {
        page,
        limit,
        totalCount: totalCount.count,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching security logs:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch security logs'
    });
  }
});

// Export content to CSV
router.get('/export/content', authenticateToken, requireModerator, logAdminAction('content_export'), async (req, res) => {
  try {
    const { type, published_only = 'false' } = req.query;

    // Build query
    let query = `
      SELECT 
        c.id, c.title, c.description, c.year, c.type, c.tmdb_id,
        c.imdb_rating, c.runtime, c.studio, c.tags, c.trailer,
        c.is_published, c.is_featured, c.add_to_carousel,
        c.total_seasons, c.total_episodes, c.created_at, c.updated_at,
        cat.name as category,
        GROUP_CONCAT(DISTINCT g.name) as genres,
        GROUP_CONCAT(DISTINCT l.name) as languages,
        GROUP_CONCAT(DISTINCT q.name) as quality,
        GROUP_CONCAT(DISTINCT a.name) as audio_tracks
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      LEFT JOIN content_genres cg ON c.id = cg.content_id
      LEFT JOIN genres g ON cg.genre_id = g.id
      LEFT JOIN content_languages cl ON c.id = cl.content_id
      LEFT JOIN languages l ON cl.language_id = l.id
      LEFT JOIN content_quality cq ON c.id = cq.content_id
      LEFT JOIN quality_options q ON cq.quality_id = q.id
      LEFT JOIN content_audio ca ON c.id = ca.content_id
      LEFT JOIN audio_tracks a ON ca.audio_id = a.id
      WHERE 1=1
    `;
    const params = [];

    if (type) {
      query += ' AND c.type = ?';
      params.push(type);
    }

    if (published_only === 'true') {
      query += ' AND c.is_published = 1';
    }

    query += ' GROUP BY c.id ORDER BY c.created_at DESC';

    const content = await db.execute(query, params);

    // Generate CSV filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `content_export_${timestamp}.csv`;
    const filepath = path.join('uploads', 'temp', filename);

    // Create CSV writer
    const writer = csvWriter.createObjectCsvWriter({
      path: filepath,
      header: [
        { id: 'id', title: 'ID' },
        { id: 'title', title: 'Title' },
        { id: 'description', title: 'Description' },
        { id: 'year', title: 'Year' },
        { id: 'type', title: 'Type' },
        { id: 'category', title: 'Category' },
        { id: 'genres', title: 'Genres' },
        { id: 'languages', title: 'Languages' },
        { id: 'quality', title: 'Quality' },
        { id: 'audio_tracks', title: 'Audio Tracks' },
        { id: 'tmdb_id', title: 'TMDB ID' },
        { id: 'imdb_rating', title: 'IMDB Rating' },
        { id: 'runtime', title: 'Runtime' },
        { id: 'studio', title: 'Studio' },
        { id: 'tags', title: 'Tags' },
        { id: 'trailer', title: 'Trailer' },
        { id: 'is_published', title: 'Published' },
        { id: 'is_featured', title: 'Featured' },
        { id: 'add_to_carousel', title: 'Carousel' },
        { id: 'total_seasons', title: 'Total Seasons' },
        { id: 'total_episodes', title: 'Total Episodes' },
        { id: 'created_at', title: 'Created At' },
        { id: 'updated_at', title: 'Updated At' }
      ]
    });

    // Write CSV file
    await writer.writeRecords(content);

    // Send file
    res.download(filepath, filename, async (err) => {
      if (err) {
        console.error('Error sending file:', err);
      }
      
      // Clean up file after download
      try {
        await fs.unlink(filepath);
      } catch (unlinkError) {
        console.error('Error cleaning up export file:', unlinkError);
      }
    });

  } catch (error) {
    console.error('Error exporting content:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to export content'
    });
  }
});

// System health check
router.get('/health', authenticateToken, requireModerator, async (req, res) => {
  try {
    // Check database connection
    const dbHealth = await db.testConnection();
    
    // Check disk space (simplified)
    const diskSpace = await fs.stat('.');
    
    // Check upload directories
    const uploadDirs = ['uploads/images', 'uploads/videos', 'uploads/subtitles'];
    const dirChecks = {};
    
    for (const dir of uploadDirs) {
      try {
        await fs.access(dir);
        dirChecks[dir] = 'accessible';
      } catch (error) {
        dirChecks[dir] = 'error';
      }
    }

    // Get system info
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development'
    };

    res.json({
      success: true,
      data: {
        database: dbHealth ? 'healthy' : 'error',
        uploadDirectories: dirChecks,
        system: systemInfo,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error checking system health:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to check system health'
    });
  }
});

module.exports = router;
