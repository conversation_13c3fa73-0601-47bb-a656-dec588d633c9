const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration for LOCAL connection (same server)
// Supports both socket (production) and TCP (development) connections
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+00:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  connectionLimit: 10,
  queueLimit: 0,
  ssl: false
};

// Use socket connection for production (more secure)
if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET) {
  dbConfig.socketPath = process.env.DB_SOCKET;
  console.log('Using MySQL socket connection for production');
} else {
  // Use TCP connection for development or when socket not available
  dbConfig.host = process.env.DB_HOST || 'localhost';
  dbConfig.port = process.env.DB_PORT || 3306;
  console.log(`Using MySQL TCP connection: ${dbConfig.host}:${dbConfig.port}`);
}

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test connection function
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('Database connection failed:', error.message);
    return false;
  }
}

// Execute query with error handling
async function executeQuery(query, params = []) {
  try {
    const [rows] = await pool.execute(query, params);
    return rows;
  } catch (error) {
    console.error('Query execution failed:', error.message);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
}

// Execute transaction
async function executeTransaction(queries) {
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const results = [];
    for (const { query, params } of queries) {
      const [result] = await connection.execute(query, params || []);
      results.push(result);
    }
    
    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

// Get content with all related data
async function getContentWithRelations(contentId) {
  const queries = {
    content: `
      SELECT c.*, cat.name as category_name, cat.slug as category_slug
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.id = ?
    `,
    genres: `
      SELECT g.id, g.name, g.slug
      FROM genres g
      JOIN content_genres cg ON g.id = cg.genre_id
      WHERE cg.content_id = ?
    `,
    languages: `
      SELECT l.id, l.name, l.code
      FROM languages l
      JOIN content_languages cl ON l.id = cl.language_id
      WHERE cl.content_id = ?
    `,
    quality: `
      SELECT q.id, q.name, q.sort_order
      FROM quality_options q
      JOIN content_quality cq ON q.id = cq.quality_id
      WHERE cq.content_id = ?
      ORDER BY q.sort_order
    `,
    audio: `
      SELECT a.id, a.name, a.code
      FROM audio_tracks a
      JOIN content_audio ca ON a.id = ca.audio_id
      WHERE ca.content_id = ?
    `,
    seasons: `
      SELECT s.*, COUNT(e.id) as episode_count
      FROM seasons s
      LEFT JOIN episodes e ON s.id = e.season_id
      WHERE s.content_id = ?
      GROUP BY s.id
      ORDER BY s.season_number
    `
  };

  try {
    const [content] = await executeQuery(queries.content, [contentId]);
    if (!content) return null;

    const [genres, languages, quality, audio, seasons] = await Promise.all([
      executeQuery(queries.genres, [contentId]),
      executeQuery(queries.languages, [contentId]),
      executeQuery(queries.quality, [contentId]),
      executeQuery(queries.audio, [contentId]),
      executeQuery(queries.seasons, [contentId])
    ]);

    return {
      ...content,
      genres,
      languages,
      quality,
      audioTracks: audio,
      seasons
    };
  } catch (error) {
    console.error('Error fetching content with relations:', error);
    throw error;
  }
}

// Get episodes for a season
async function getSeasonEpisodes(seasonId) {
  const query = `
    SELECT * FROM episodes
    WHERE season_id = ?
    ORDER BY episode_number
  `;
  
  return await executeQuery(query, [seasonId]);
}

// Search content with filters
async function searchContent(filters = {}) {
  let query = `
    SELECT DISTINCT c.*, cat.name as category_name, cat.slug as category_slug
    FROM content c
    LEFT JOIN categories cat ON c.category_id = cat.id
    LEFT JOIN content_genres cg ON c.id = cg.content_id
    LEFT JOIN genres g ON cg.genre_id = g.id
    LEFT JOIN content_languages cl ON c.id = cl.content_id
    LEFT JOIN languages l ON cl.language_id = l.id
    WHERE 1=1
  `;
  
  const params = [];
  
  // Add filters
  if (filters.type) {
    query += ' AND c.type = ?';
    params.push(filters.type);
  }
  
  if (filters.category) {
    query += ' AND cat.slug = ?';
    params.push(filters.category);
  }
  
  if (filters.genre) {
    query += ' AND g.slug = ?';
    params.push(filters.genre);
  }
  
  if (filters.language) {
    query += ' AND l.code = ?';
    params.push(filters.language);
  }
  
  if (filters.year) {
    query += ' AND c.year = ?';
    params.push(filters.year);
  }
  
  if (filters.featured !== undefined) {
    query += ' AND c.is_featured = ?';
    params.push(filters.featured);
  }
  
  if (filters.published !== undefined) {
    query += ' AND c.is_published = ?';
    params.push(filters.published);
  }
  
  if (filters.search) {
    query += ' AND (MATCH(c.title, c.description, c.tags) AGAINST(? IN NATURAL LANGUAGE MODE) OR c.title LIKE ?)';
    params.push(filters.search, `%${filters.search}%`);
  }
  
  // Add sorting
  const sortBy = filters.sortBy || 'created_at';
  const sortOrder = filters.sortOrder || 'DESC';
  query += ` ORDER BY c.${sortBy} ${sortOrder}`;
  
  // Add pagination
  if (filters.limit) {
    query += ' LIMIT ?';
    params.push(parseInt(filters.limit));
    
    if (filters.offset) {
      query += ' OFFSET ?';
      params.push(parseInt(filters.offset));
    }
  }
  
  return await executeQuery(query, params);
}

// Database utility functions
const dbUtils = {
  // Get all categories
  getCategories: () => executeQuery('SELECT * FROM categories WHERE is_active = 1 ORDER BY name'),
  
  // Get all genres
  getGenres: () => executeQuery('SELECT * FROM genres ORDER BY name'),
  
  // Get all languages
  getLanguages: () => executeQuery('SELECT * FROM languages ORDER BY name'),
  
  // Get all quality options
  getQualityOptions: () => executeQuery('SELECT * FROM quality_options ORDER BY sort_order'),
  
  // Get all audio tracks
  getAudioTracks: () => executeQuery('SELECT * FROM audio_tracks ORDER BY name'),
  
  // Get content count by type
  getContentStats: () => executeQuery(`
    SELECT 
      type,
      COUNT(*) as count,
      SUM(CASE WHEN is_published = 1 THEN 1 ELSE 0 END) as published_count,
      SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured_count
    FROM content 
    GROUP BY type
  `)
};

module.exports = {
  pool,
  execute: executeQuery,
  executeTransaction,
  testConnection,
  getContentWithRelations,
  getSeasonEpisodes,
  searchContent,
  ...dbUtils
};
