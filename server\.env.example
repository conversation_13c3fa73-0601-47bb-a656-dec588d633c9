# StreamDB Online - Production Environment Configuration
# Database Configuration for Alexhost VPS - LOCAL CONNECTION (ULTRA SECURE)
# Since website and database are on the same server, use local socket connection
DB_HOST=localhost
# No DB_PORT needed - uses MySQL socket for maximum security
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=streamdb_database
DB_USER=dbadmin_streamdb
DB_PASSWORD=your_actual_database_password_from_fastpanel
# No SSL needed for local socket connections

# JWT Configuration
JWT_SECRET=your_very_long_random_jwt_secret_key_here_change_this_in_production
JWT_EXPIRES_IN=24h

# Session Configuration
SESSION_SECRET=your_very_long_random_session_secret_key_here_change_this_in_production
SESSION_TIMEOUT=86400000

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# CORS Configuration
FRONTEND_URL=https://streamdb.online
CORS_ORIGIN=https://streamdb.online,http://localhost:5173

# Server Configuration
PORT=3001
NODE_ENV=production

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp,image/gif
ALLOWED_VIDEO_TYPES=video/mp4,video/webm,video/ogg

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5

# Logging
LOG_LEVEL=info
LOG_FILE=logs/server.log

# Webhook Configuration for Auto-Deployment
WEBHOOK_SECRET=your_webhook_secret_for_github_auto_deployment_change_this
WEBHOOK_PORT=9000

# Admin User Configuration (for initial setup only)
ADMIN_USERNAME=streamdb_admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_ultra_secure_admin_password_here_change_this

# Email Configuration (for future features)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
SMTP_FROM=<EMAIL>

# External API Keys (for future integrations)
TMDB_API_KEY=your_tmdb_api_key_here
OMDB_API_KEY=your_omdb_api_key_here

# CDN Configuration (for future use)
CDN_URL=https://cdn.your-domain.com
CDN_ACCESS_KEY=your_cdn_access_key
CDN_SECRET_KEY=your_cdn_secret_key

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=/backups

# Monitoring and Analytics
ANALYTICS_ENABLED=false
ANALYTICS_API_KEY=your_analytics_key
MONITORING_ENABLED=false
MONITORING_API_KEY=your_monitoring_key
