# Server-Side Authentication Implementation Guide

## Overview

This guide provides comprehensive instructions for implementing secure server-side authentication for your StreamDB application. The current implementation uses client-side demo credentials for development, but this guide will help you transition to a production-ready server-side authentication system.

## Technology Stack Options

### Option 1: Node.js + Express + JWT

**Dependencies:**
```bash
npm install express bcryptjs jsonwebtoken cors helmet express-rate-limit
npm install --save-dev @types/express @types/bcryptjs @types/jsonwebtoken
```

**Basic Server Setup:**
```javascript
// server.js
const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:8080',
  credentials: true
}));

// Rate limiting
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: 'Too many authentication attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/auth', authLimiter);
app.use(express.json({ limit: '10mb' }));

// Database connection (example with MySQL)
const mysql = require('mysql2/promise');
const db = mysql.createConnection({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
});
```

### Option 2: Python + FastAPI + JWT

**Dependencies:**
```bash
pip install fastapi uvicorn python-jose[cryptography] passlib[bcrypt] python-multipart
```

**Basic Server Setup:**
```python
# main.py
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime, timedelta
import os

app = FastAPI()

# Security setup
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

app.add_middleware(
    CORSMiddleware,
    allow_origins=[os.getenv("FRONTEND_URL", "http://localhost:8080")],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## Authentication Endpoints

### 1. Login Endpoint

**Node.js Implementation:**
```javascript
// POST /api/auth/login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // Input validation
    if (!username || !password) {
      return res.status(400).json({ 
        success: false, 
        error: 'Username and password are required' 
      });
    }

    // Get user from database
    const [users] = await db.execute(
      'SELECT * FROM admin_users WHERE username = ? AND is_active = TRUE',
      [username]
    );

    if (users.length === 0) {
      // Log failed attempt
      await logSecurityEvent('LOGIN_FAILED', { username, reason: 'User not found' });
      return res.status(401).json({ 
        success: false, 
        error: 'Invalid credentials' 
      });
    }

    const user = users[0];

    // Check if account is locked
    if (user.locked_until && new Date() < user.locked_until) {
      await logSecurityEvent('LOGIN_FAILED', { username, reason: 'Account locked' });
      return res.status(423).json({ 
        success: false, 
        error: 'Account is locked. Please try again later.' 
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);

    if (!isValidPassword) {
      // Increment failed attempts
      await db.execute(
        'UPDATE admin_users SET failed_login_attempts = failed_login_attempts + 1 WHERE id = ?',
        [user.id]
      );

      // Lock account if too many attempts
      if (user.failed_login_attempts >= 4) {
        const lockUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        await db.execute(
          'UPDATE admin_users SET locked_until = ? WHERE id = ?',
          [lockUntil, user.id]
        );
      }

      await logSecurityEvent('LOGIN_FAILED', { username, reason: 'Invalid password' });
      return res.status(401).json({ 
        success: false, 
        error: 'Invalid credentials' 
      });
    }

    // Reset failed attempts on successful login
    await db.execute(
      'UPDATE admin_users SET failed_login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE id = ?',
      [user.id]
    );

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username, 
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Create session record
    const sessionId = require('crypto').randomBytes(32).toString('hex');
    await db.execute(
      'INSERT INTO admin_sessions (id, user_id, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, ?)',
      [sessionId, user.id, req.ip, req.get('User-Agent'), new Date(Date.now() + 24 * 60 * 60 * 1000)]
    );

    await logSecurityEvent('LOGIN_SUCCESS', { userId: user.id, sessionId });

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        permissions: JSON.parse(user.permissions || '[]')
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    await logSecurityEvent('LOGIN_ERROR', { error: error.message });
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});
```

**Python Implementation:**
```python
from pydantic import BaseModel

class LoginRequest(BaseModel):
    username: str
    password: str

@app.post("/api/auth/login")
async def login(credentials: LoginRequest):
    try:
        # Get user from database
        user = await get_user_by_username(credentials.username)
        
        if not user or not user.is_active:
            await log_security_event("LOGIN_FAILED", {"username": credentials.username, "reason": "User not found"})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        # Check if account is locked
        if user.locked_until and datetime.now() < user.locked_until:
            await log_security_event("LOGIN_FAILED", {"username": credentials.username, "reason": "Account locked"})
            raise HTTPException(
                status_code=status.HTTP_423_LOCKED,
                detail="Account is locked. Please try again later."
            )

        # Verify password
        if not pwd_context.verify(credentials.password, user.password_hash):
            await increment_failed_attempts(user.id)
            await log_security_event("LOGIN_FAILED", {"username": credentials.username, "reason": "Invalid password"})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        # Reset failed attempts
        await reset_failed_attempts(user.id)

        # Generate JWT token
        access_token_expires = timedelta(hours=24)
        access_token = create_access_token(
            data={"sub": str(user.id), "username": user.username, "role": user.role},
            expires_delta=access_token_expires
        )

        await log_security_event("LOGIN_SUCCESS", {"userId": user.id})

        return {
            "success": True,
            "token": access_token,
            "user": {
                "id": user.id,
                "username": user.username,
                "role": user.role,
                "permissions": user.permissions
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        await log_security_event("LOGIN_ERROR", {"error": str(e)})
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
```

### 2. Token Verification Middleware

**Node.js Implementation:**
```javascript
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if user still exists and is active
    const [users] = await db.execute(
      'SELECT * FROM admin_users WHERE id = ? AND is_active = TRUE',
      [decoded.userId]
    );

    if (users.length === 0) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    req.user = users[0];
    next();

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Protect admin routes
app.use('/api/admin/*', verifyToken);
```

**Python Implementation:**
```python
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        token = credentials.credentials
        payload = jwt.decode(token, os.getenv("JWT_SECRET"), algorithms=["HS256"])
        user_id: str = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
            
        user = await get_user_by_id(int(user_id))
        if user is None or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
            
        return user
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

# Protected route example
@app.get("/api/admin/profile")
async def get_admin_profile(current_user = Depends(get_current_user)):
    return {
        "id": current_user.id,
        "username": current_user.username,
        "role": current_user.role
    }
```

### 3. Logout Endpoint

**Node.js Implementation:**
```javascript
app.post('/api/auth/logout', verifyToken, async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    
    // Add token to blacklist (implement token blacklisting)
    await addTokenToBlacklist(token);
    
    // Remove session from database
    await db.execute(
      'DELETE FROM admin_sessions WHERE user_id = ?',
      [req.user.id]
    );

    await logSecurityEvent('LOGOUT', { userId: req.user.id });

    res.json({ success: true, message: 'Logged out successfully' });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});
```

## Security Best Practices

### 1. Environment Variables

```env
# .env (server-side)
NODE_ENV=production
PORT=3001

# Database
DB_HOST=localhost
DB_USER=streamdb_app
DB_PASSWORD=your_secure_database_password
DB_NAME=streamdb

# JWT
JWT_SECRET=your_very_long_random_jwt_secret_key_here_at_least_32_characters
JWT_EXPIRES_IN=24h

# Security
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# CORS
FRONTEND_URL=https://yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_ATTEMPTS=5
```

### 2. Security Headers

```javascript
// Additional security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.setHeader('Content-Security-Policy', "default-src 'self'");
  next();
});
```

### 3. Input Validation

```javascript
const { body, validationResult } = require('express-validator');

const validateLogin = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-50 characters and contain only letters, numbers, and underscores'),
  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be 8-128 characters long'),
];

app.post('/api/auth/login', validateLogin, async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      success: false, 
      errors: errors.array() 
    });
  }
  // ... rest of login logic
});
```

## Frontend Integration

### Update AuthContext for Server-Side Authentication

```typescript
// Update src/contexts/AuthContext.tsx
const login = useCallback(async (credentials: LoginCredentials): Promise<LoginResponse> => {
  dispatch({ type: 'LOGIN_START' });

  try {
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    const data = await response.json();

    if (!response.ok) {
      LoginAttemptTracker.recordAttempt(false);
      SecurityLogger.logEvent('LOGIN_FAILED', { username: credentials.username }, 'medium');
      
      dispatch({ type: 'LOGIN_FAILURE', payload: data.error });
      return { success: false, error: data.error };
    }

    // Successful login
    LoginAttemptTracker.recordAttempt(true);
    LoginAttemptTracker.clearAttempts();

    const sessionData: SessionData = {
      user: data.user,
      token: data.token,
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      createdAt: Date.now(),
    };

    SecureSessionManager.createSession(sessionData);

    dispatch({
      type: 'LOGIN_SUCCESS',
      payload: {
        user: data.user,
        sessionExpiry: sessionData.expiresAt,
      },
    });

    SecurityLogger.logEvent('LOGIN_SUCCESS', { userId: data.user.id });

    return { success: true, user: data.user, token: data.token };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Network error';
    SecurityLogger.logEvent('LOGIN_FAILED', { error: errorMessage }, 'high');
    
    dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
    return { success: false, error: errorMessage };
  }
}, []);
```

### API Request Interceptor

```typescript
// src/utils/apiClient.ts
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export class ApiClient {
  private static getAuthToken(): string | null {
    const session = SecureSessionManager.getSession();
    return session?.token || null;
  }

  static async request(endpoint: string, options: RequestInit = {}): Promise<Response> {
    const token = this.getAuthToken();
    
    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

    // Handle token expiry
    if (response.status === 401) {
      SecureSessionManager.destroySession();
      window.location.href = '/login';
    }

    return response;
  }

  static async get(endpoint: string): Promise<Response> {
    return this.request(endpoint, { method: 'GET' });
  }

  static async post(endpoint: string, data: any): Promise<Response> {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}
```

## Deployment Considerations

### 1. HTTPS Configuration

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
}
```

### 2. Database Security

```sql
-- Create dedicated database user
CREATE USER 'streamdb_api'@'localhost' IDENTIFIED BY 'secure_random_password';

-- Grant minimal necessary permissions
GRANT SELECT, INSERT, UPDATE ON streamdb.admin_users TO 'streamdb_api'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb.admin_sessions TO 'streamdb_api'@'localhost';
GRANT INSERT ON streamdb.admin_security_logs TO 'streamdb_api'@'localhost';

FLUSH PRIVILEGES;
```

### 3. Monitoring and Logging

```javascript
// Security monitoring
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'security.log' }),
    new winston.transports.Console()
  ]
});

async function logSecurityEvent(event, details) {
  logger.info('Security Event', {
    event,
    details,
    timestamp: new Date().toISOString(),
    ip: details.ip || 'unknown'
  });

  // Store in database
  await db.execute(
    'INSERT INTO admin_security_logs (event_type, details, created_at) VALUES (?, ?, NOW())',
    [event, JSON.stringify(details)]
  );
}
```

## Testing the Implementation

### 1. Test Authentication Flow

```bash
# Test login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "your_password"}'

# Test protected route
curl -X GET http://localhost:3001/api/admin/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test logout
curl -X POST http://localhost:3001/api/auth/logout \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Load Testing

```bash
# Install artillery for load testing
npm install -g artillery

# Create test script (artillery-test.yml)
config:
  target: 'http://localhost:3001'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Login flow"
    requests:
      - post:
          url: "/api/auth/login"
          json:
            username: "admin"
            password: "your_password"

# Run load test
artillery run artillery-test.yml
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure frontend URL is in CORS whitelist
2. **JWT Errors**: Check JWT_SECRET environment variable
3. **Database Connection**: Verify database credentials and connectivity
4. **Rate Limiting**: Adjust rate limits for your use case

### Debug Mode

```javascript
// Enable debug logging
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.path}`, req.body);
    next();
  });
}
```

This guide provides a comprehensive foundation for implementing secure server-side authentication. Always test thoroughly in a development environment before deploying to production.
