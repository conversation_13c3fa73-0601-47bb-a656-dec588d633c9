<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Image Loading Debug - Telegram Banner</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: #0a0a0a;
            color: #e6cb8e;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #1a1a1a;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #333;
        }
        
        h1 {
            color: #e6cb8e;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .test-card {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #444;
        }
        
        .test-title {
            font-weight: bold;
            margin-bottom: 1rem;
            color: #e6cb8e;
        }
        
        .test-image {
            width: 100%;
            height: 150px;
            border-radius: 8px;
            object-fit: cover;
            background: linear-gradient(to right, #2fbdfa 0%, #2da1fa 100%);
            margin-bottom: 0.5rem;
        }
        
        .status {
            font-size: 0.9rem;
            padding: 0.5rem;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        
        .status.loading { background: #1a1a2a; color: #339af0; }
        .status.success { background: #1a2a1a; color: #51cf66; }
        .status.error { background: #2a1a1a; color: #ff6b6b; }
        
        .info-section {
            background: #1a1a2a;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .log {
            background: #0a0a0a;
            border-radius: 4px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.8rem;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        button {
            background: #e6cb8e;
            color: #0a0a0a;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        
        button:hover {
            background: #d4b574;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Image Loading Debug - Telegram Banner</h1>
        
        <div class="info-section">
            <h3>Environment Information</h3>
            <div id="env-info">Loading...</div>
        </div>
        
        <div class="info-section">
            <h3>Test Controls</h3>
            <button onclick="runTests()">🔄 Run Tests</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
            <button onclick="downloadLog()">💾 Download Log</button>
        </div>
        
        <div class="test-grid" id="test-grid">
            <!-- Test cards will be populated by JavaScript -->
        </div>
        
        <div class="info-section">
            <h3>Debug Log</h3>
            <div class="log" id="debug-log"></div>
        </div>
    </div>

    <script>
        const imagePaths = [
            "/telegram-banner.png",
            "/telegram-banner-backup.png", 
            "./telegram-banner.png",
            "/cloudflare-1111-banner.png",
            "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDQwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIwJSI+PHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzJmYmRmYTtzdG9wLW9wYWNpdHk6MSIgLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMyZGExZmE7c3RvcC1vcGFjaXR5OjEiIC8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PHJlY3Qgd2lkdGg9IjQwMCIgaGVpZ2h0PSIxNTAiIGZpbGw9InVybCgjZ3JhZCkiIHJ4PSIxMiIvPjx0ZXh0IHg9IjIwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJzeXN0ZW0tdWksIC1hcHBsZS1zeXN0ZW0sIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7wn5OxPC90ZXh0Pjx0ZXh0IHg9IjIwMCIgeT0iOTAiIGZvbnQtZmFtaWx5PSJzeXN0ZW0tdWksIC1hcHBsZS1zeXN0ZW0sIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Sm9pbiBvdXIgVGVsZWdyYW0gQ2hhbm5lbDwvdGV4dD48dGV4dCB4PSIyMDAiIHk9IjExNSIgZm9udC1mYW1pbHk9InN5c3RlbS11aSwgLWFwcGxlLXN5c3RlbSwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIG9wYWNpdHk9IjAuOSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+TGF0ZXN0IHVwbG9hZHMgJmFtcDsgZXhjbHVzaXZlIGNvbnRlbnQ8L3RleHQ+PC9zdmc+"
        ];
        
        let debugLog = [];
        
        function log(message) {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            
            const logElement = document.getElementById('debug-log');
            logElement.innerHTML = debugLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }
        
        function updateStatus(cardId, status, message) {
            const statusElement = document.querySelector(`#${cardId} .status`);
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }
        
        async function testImage(path, index) {
            const cardId = `test-card-${index}`;
            log(`Testing image: ${path}`);
            updateStatus(cardId, 'loading', 'Loading...');
            
            try {
                const startTime = Date.now();
                const img = new Image();
                
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('Timeout after 10 seconds'));
                    }, 10000);
                    
                    img.onload = () => {
                        clearTimeout(timeout);
                        const loadTime = Date.now() - startTime;
                        log(`✅ SUCCESS: ${path} (${loadTime}ms, ${img.naturalWidth}x${img.naturalHeight})`);
                        updateStatus(cardId, 'success', `✅ Loaded in ${loadTime}ms (${img.naturalWidth}x${img.naturalHeight})`);
                        resolve();
                    };
                    
                    img.onerror = (error) => {
                        clearTimeout(timeout);
                        log(`❌ ERROR: ${path} - ${error}`);
                        updateStatus(cardId, 'error', `❌ Failed to load`);
                        reject(error);
                    };
                });
                
                img.src = path;
                
            } catch (error) {
                log(`❌ EXCEPTION: ${path} - ${error.message}`);
                updateStatus(cardId, 'error', `❌ ${error.message}`);
            }
        }
        
        function createTestCard(path, index) {
            const isDataUrl = path.startsWith('data:');
            const displayPath = isDataUrl ? 'SVG Fallback (Data URL)' : path;
            
            return `
                <div class="test-card" id="test-card-${index}">
                    <div class="test-title">${displayPath}</div>
                    <img src="${path}" alt="Test image" class="test-image" 
                         onload="updateStatus('test-card-${index}', 'success', '✅ Image element loaded')"
                         onerror="updateStatus('test-card-${index}', 'error', '❌ Image element failed')">
                    <div class="status loading">Ready to test</div>
                </div>
            `;
        }
        
        function initializeTests() {
            const testGrid = document.getElementById('test-grid');
            testGrid.innerHTML = imagePaths.map((path, index) => createTestCard(path, index)).join('');
            
            // Display environment info
            const envInfo = document.getElementById('env-info');
            envInfo.innerHTML = `
                <strong>Host:</strong> ${window.location.host}<br>
                <strong>Protocol:</strong> ${window.location.protocol}<br>
                <strong>User Agent:</strong> ${navigator.userAgent}<br>
                <strong>Is Vercel:</strong> ${window.location.host.includes('vercel') ? 'Yes' : 'No'}<br>
                <strong>Is Localhost:</strong> ${window.location.host.includes('localhost') ? 'Yes' : 'No'}<br>
                <strong>Current URL:</strong> ${window.location.href}
            `;
            
            log('🚀 Image debug page initialized');
            log(`Environment: ${window.location.host} (${window.location.protocol})`);
        }
        
        async function runTests() {
            log('🔄 Starting image loading tests...');
            
            for (let i = 0; i < imagePaths.length; i++) {
                await testImage(imagePaths[i], i);
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            log('✅ All tests completed');
        }
        
        function clearLog() {
            debugLog = [];
            document.getElementById('debug-log').innerHTML = '';
            log('🗑️ Log cleared');
        }
        
        function downloadLog() {
            const logContent = debugLog.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `telegram-banner-debug-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            log('💾 Debug log downloaded');
        }
        
        // Initialize when page loads
        window.addEventListener('load', initializeTests);
    </script>
</body>
</html>
