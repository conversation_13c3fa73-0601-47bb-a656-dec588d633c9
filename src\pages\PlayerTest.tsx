import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SecureVideoPlayer from '@/components/SecureVideoPlayer';
import { encodeVideoLinks, parseVideoLinks, isValidVideoLink } from '@/utils/videoSecurity';
import { runAllEmbedLinkTests, testEmbedLinks } from '@/test/embed-link-validation.test';
import { runAll2EmbedDebug } from '@/test/2embed-validation-debug';
import { runSecurityValidationCompatibilityTests } from '@/test/security-validation-compatibility.test';
import { runUserFailingLinksTests } from '@/test/user-failing-links.test';
import { runIFrameConfigTests } from '@/test/iframe-config.test';
import { runAllSandboxRemovalTests } from '@/test/sandbox-removal-test';
import { ArrowLeft, Play, TestTube, CheckCircle, AlertTriangle, Bug, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function PlayerTest() {
  const [testLinks, setTestLinks] = useState('');
  const [encodedLinks, setEncodedLinks] = useState('');
  const [testResults, setTestResults] = useState<any[]>([]);

  // Enhanced sample test data with multiple platforms
  const sampleEmbedLinks = `https://www.youtube.com/embed/dQw4w9WgXcQ
https://player.vimeo.com/video/123456789
//player.example.com/embed/movie123
<iframe src="https://streamable.com/e/abc123" allowfullscreen></iframe>
https://www.dailymotion.com/embed/video/x123456
https://player.twitch.tv/embed/video/123456789`;

  // User's previously failing links (now fixed)
  const userFailingLinks = `https://gradehgplus.com/e/xvay1ggua7s7
https://streamtape.com/v/YeRw6amy3MsvWa7/Dont.Leave.2022.720p.WEB-DL.English.ESubs.MoviesMod.com.mkv
https://filemoon.to/e/ezfjmgsjwwsh`;

  const handleEncodeTest = () => {
    if (!testLinks.trim()) {
      setTestLinks(sampleEmbedLinks);
      return;
    }

    const encoded = encodeVideoLinks(testLinks);
    setEncodedLinks(encoded);

    // Test parsing and validation
    const links = parseVideoLinks(testLinks);
    const results = links.map((link, index) => ({
      index: index + 1,
      link: link.substring(0, 50) + (link.length > 50 ? '...' : ''),
      isValid: isValidVideoLink(link),
      playerName: `Player ${index + 1}`
    }));

    setTestResults(results);
  };

  const handleClearTest = () => {
    setTestLinks('');
    setEncodedLinks('');
    setTestResults([]);
  };

  const handleRunValidationTests = () => {
    console.log('🧪 Running comprehensive embed link validation tests...');
    const success = runAllEmbedLinkTests();

    if (success) {
      alert('✅ All validation tests passed! Check console for details.');
    } else {
      alert('⚠️ Some validation tests failed. Check console for details.');
    }
  };

  const handleDebug2Embed = () => {
    console.log('🔍 Running 2embed.cc validation debug...');
    runAll2EmbedDebug();
    alert('🔍 2embed.cc debug completed! Check console for detailed analysis.');
  };

  const handleTestSecurity = () => {
    console.log('🔒 Running security + validation compatibility tests...');
    const success = runSecurityValidationCompatibilityTests();

    if (success) {
      alert('✅ All security compatibility tests passed! Validation and encoding work together perfectly.');
    } else {
      alert('⚠️ Some security compatibility issues found. Check console for details.');
    }
  };

  const handleTestUserFailingLinks = () => {
    console.log('🔧 Testing user\'s previously failing embed links...');
    const success = runUserFailingLinksTests();

    if (success) {
      alert('🎉 All user failing links now pass validation! The fix is working correctly.');
    } else {
      alert('⚠️ Some user failing links still have issues. Check console for details.');
    }
  };

  const handleTestIFrameConfig = () => {
    console.log('🔧 Testing iFrame configurations for embed platforms...');
    const success = runIFrameConfigTests();

    if (success) {
      alert('🎉 All iFrame configurations are correct! Embedding issues should be resolved.');
    } else {
      alert('⚠️ Some iFrame configuration issues found. Check console for details.');
    }
  };

  const handleTestSandboxRemoval = () => {
    console.log('🛡️ Testing sandbox restriction removal and enhanced security...');
    const success = runAllSandboxRemovalTests();

    if (success) {
      alert('🎉 Sandbox restrictions successfully removed! Video playback should work across all platforms.');
    } else {
      alert('⚠️ Some sandbox removal issues found. Check console for details.');
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      
      <main className="flex-1 max-w-6xl w-full mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-primary mb-2">Video Player Testing</h1>
            <p className="text-muted-foreground">Test the secure video player functionality and embed link processing</p>
          </div>
          <Link
            to="/admin"
            className="inline-flex items-center gap-2 px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Admin
          </Link>
        </div>

        <Tabs defaultValue="test" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="test">Test Player</TabsTrigger>
            <TabsTrigger value="aspect-ratio">Aspect Ratio</TabsTrigger>
            <TabsTrigger value="security">Security Test</TabsTrigger>
            <TabsTrigger value="integration">Integration Test</TabsTrigger>
          </TabsList>

          <TabsContent value="test" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TestTube className="w-5 h-5" />
                  Video Player Test
                </CardTitle>
                <CardDescription>
                  Test the video player with different embed link formats
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Test Embed Links</Label>
                  <Textarea
                    placeholder="Enter video embed links (one per line)..."
                    value={testLinks}
                    onChange={(e) => setTestLinks(e.target.value)}
                    className="min-h-[120px] font-mono text-sm"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Supports YouTube, Vimeo, and custom embed URLs
                  </p>
                </div>

                <div className="flex gap-2 flex-wrap">
                  <Button onClick={handleEncodeTest}>
                    <Play className="w-4 h-4 mr-2" />
                    Test Player
                  </Button>
                  <Button variant="outline" onClick={() => setTestLinks(sampleEmbedLinks)}>
                    Load Sample Data
                  </Button>
                  <Button variant="outline" onClick={() => setTestLinks(userFailingLinks)}>
                    Load User Fix Test
                  </Button>
                  <Button variant="outline" onClick={handleRunValidationTests}>
                    <Bug className="w-4 h-4 mr-2" />
                    Run Validation Tests
                  </Button>
                  <Button variant="outline" onClick={handleDebug2Embed}>
                    <Bug className="w-4 h-4 mr-2" />
                    Debug 2embed.cc
                  </Button>
                  <Button variant="outline" onClick={handleTestSecurity}>
                    <Bug className="w-4 h-4 mr-2" />
                    Test Security
                  </Button>
                  <Button variant="outline" onClick={handleTestUserFailingLinks}>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Test User Fix
                  </Button>
                  <Button variant="outline" onClick={handleTestIFrameConfig}>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Test iFrame Config
                  </Button>
                  <Button variant="outline" onClick={handleTestSandboxRemoval}>
                    <Shield className="w-4 h-4 mr-2" />
                    Test Sandbox Fix
                  </Button>
                  <Button variant="outline" onClick={handleClearTest}>
                    Clear
                  </Button>
                </div>

                {testResults.length > 0 && (
                  <div className="space-y-2">
                    <Label>Link Validation Results</Label>
                    <div className="grid gap-2">
                      {testResults.map((result) => (
                        <div key={result.index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{result.playerName}</Badge>
                            <span className="text-sm font-mono">{result.link}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            {result.isValid ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <AlertTriangle className="w-4 h-4 text-red-500" />
                            )}
                            <span className="text-xs">
                              {result.isValid ? 'Valid' : 'Invalid'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {encodedLinks && (
              <Card>
                <CardHeader>
                  <CardTitle>Live Player Preview</CardTitle>
                  <CardDescription>
                    Testing the secure video player with encoded links
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <SecureVideoPlayer
                    encodedVideoLinks={encodedLinks}
                    title="Test Video Player"
                    showPlayerSelection={true}
                    className="max-w-4xl"
                  />
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="aspect-ratio" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TestTube className="w-5 h-5" />
                  Dynamic Aspect Ratio Testing
                </CardTitle>
                <CardDescription>
                  Test the new dynamic aspect ratio detection and responsive design features
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Test Controls */}
                  <div className="space-y-4">
                    <div>
                      <Label>Test Video URL</Label>
                      <Textarea
                        placeholder="Enter a single video embed URL to test aspect ratio detection..."
                        value={testLinks}
                        onChange={(e) => setTestLinks(e.target.value)}
                        className="min-h-[80px] font-mono text-sm"
                      />
                    </div>

                    <div className="flex gap-2 flex-wrap">
                      <Button onClick={handleEncodeTest}>
                        <Play className="w-4 h-4 mr-2" />
                        Test Dynamic Aspect Ratio
                      </Button>
                      <Button variant="outline" onClick={() => setTestLinks('https://www.youtube.com/embed/dQw4w9WgXcQ')}>
                        YouTube Sample
                      </Button>
                      <Button variant="outline" onClick={() => setTestLinks('https://player.vimeo.com/video/123456789')}>
                        Vimeo Sample
                      </Button>
                    </div>
                  </div>

                  {/* Feature Overview */}
                  <div className="space-y-4">
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <h4 className="font-medium mb-2">New Features:</h4>
                      <ul className="text-sm space-y-1 text-muted-foreground">
                        <li>✓ Automatic platform detection (YouTube, Vimeo, etc.)</li>
                        <li>✓ Dynamic aspect ratio based on content type</li>
                        <li>✓ Responsive scaling for mobile devices</li>
                        <li>✓ Mobile-friendly ratio enforcement</li>
                        <li>✓ Backward compatibility with existing players</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Test Results */}
                {encodedLinks && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Standard Player (Backward Compatibility) */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Standard Player (16:9)</CardTitle>
                          <CardDescription>
                            Traditional fixed aspect ratio for comparison
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <SecureVideoPlayer
                            encodedVideoLinks={encodedLinks}
                            title="Standard Player"
                            showPlayerSelection={false}
                            enableDynamicAspectRatio={false}
                            className="w-full"
                          />
                        </CardContent>
                      </Card>

                      {/* Dynamic Player */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Dynamic Aspect Ratio Player</CardTitle>
                          <CardDescription>
                            Auto-detected optimal aspect ratio with responsive design
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <SecureVideoPlayer
                            encodedVideoLinks={encodedLinks}
                            title="Dynamic Player"
                            showPlayerSelection={false}
                            enableDynamicAspectRatio={true}
                            enableResponsive={true}
                            className="w-full"
                          />
                        </CardContent>
                      </Card>
                    </div>

                    {/* Mobile-Friendly Player */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Mobile-Optimized Player</CardTitle>
                        <CardDescription>
                          Dynamic aspect ratio with mobile-friendly enforcement
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <SecureVideoPlayer
                          encodedVideoLinks={encodedLinks}
                          title="Mobile-Optimized Player"
                          showPlayerSelection={false}
                          enableDynamicAspectRatio={true}
                          forceMobileFriendly={true}
                          enableResponsive={true}
                          className="w-full max-w-md mx-auto"
                        />
                      </CardContent>
                    </Card>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Security Features Test</CardTitle>
                <CardDescription>
                  Verify that embed links are properly secured and not exposed in client-side code
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Original Links (Admin View)</Label>
                    <Textarea
                      value={testLinks}
                      onChange={(e) => setTestLinks(e.target.value)}
                      className="min-h-[100px] font-mono text-xs"
                      placeholder="Enter embed URLs here to test security encoding..."
                    />
                  </div>
                  <div>
                    <Label>Encoded Links (Client Storage)</Label>
                    <Textarea
                      value={encodedLinks}
                      readOnly
                      className="min-h-[100px] font-mono text-xs"
                      placeholder="Encoded links will appear here..."
                    />
                  </div>
                </div>

                <div className="flex gap-2 flex-wrap">
                  <Button onClick={handleEncodeTest} variant="default">
                    🔒 Encode & Test Security
                  </Button>
                  <Button onClick={() => setTestLinks(sampleEmbedLinks)} variant="outline">
                    📝 Load Sample URLs
                  </Button>
                  <Button onClick={handleClearTest} variant="outline">
                    🗑️ Clear All
                  </Button>
                </div>
                
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-medium mb-2">Security Features:</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>✓ Embed links are XOR encoded with a secret key</li>
                    <li>✓ Base64 URL-safe encoding for storage</li>
                    <li>✓ Timestamp and salt added for additional obfuscation</li>
                    <li>✓ Links are validated for security patterns</li>
                    <li>✓ Original links never stored in plain text client-side</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="integration" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Admin Panel Integration</CardTitle>
                <CardDescription>
                  Test how the player integrates with the admin panel workflow
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Integration Workflow:</h4>
                  <ol className="text-sm space-y-2 text-muted-foreground">
                    <li>1. Admin enters embed links in the "Add Video Embed Link(s)" field</li>
                    <li>2. Links are automatically validated and encoded for security</li>
                    <li>3. Preview player shows real-time preview with player selection</li>
                    <li>4. Multiple links are auto-named as "Player 1", "Player 2", etc.</li>
                    <li>5. Secure links are saved to database (encoded format)</li>
                    <li>6. Public users see the player without access to original links</li>
                  </ol>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                    <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">✓ Working Features</h4>
                    <ul className="text-sm space-y-1 text-green-700 dark:text-green-300">
                      <li>• Auto-encoding of video links</li>
                      <li>• Real-time preview in admin panel</li>
                      <li>• Multiple player support</li>
                      <li>• Auto-naming system</li>
                      <li>• Security validation</li>
                    </ul>
                  </div>
                  
                  <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">🔄 Future Enhancements</h4>
                    <ul className="text-sm space-y-1 text-blue-700 dark:text-blue-300">
                      <li>• Server-side proxy for ultimate security</li>
                      <li>• Content page routing</li>
                      <li>• Episode-specific players for series</li>
                      <li>• Player analytics</li>
                      <li>• Custom player themes</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      <Footer />
    </div>
  );
}
