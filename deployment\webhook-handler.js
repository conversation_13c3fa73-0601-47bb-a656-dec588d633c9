#!/usr/bin/env node

/**
 * StreamDB Online - GitHub Webhook Handler
 * 
 * Secure webhook handler for automatic deployment from GitHub
 * Features:
 * - Signature verification
 * - Branch filtering
 * - Rate limiting
 * - Deployment logging
 * - Error handling
 */

const http = require('http');
const crypto = require('crypto');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Configuration
const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;
const WEBHOOK_PORT = process.env.WEBHOOK_PORT || 9000;
const DEPLOY_SCRIPT = process.env.DEPLOY_SCRIPT || '/var/www/streamdb_onl_usr/data/www/streamdb.online/deployment/deploy.sh';
const LOG_FILE = process.env.WEBHOOK_LOG_FILE || '/var/log/webhook-handler.log';
const ALLOWED_BRANCH = process.env.DEPLOY_BRANCH || 'main';
const MAX_DEPLOYMENTS_PER_HOUR = 10;

// Rate limiting storage
const deploymentHistory = [];

// Utility functions
function log(level, message, data = null) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    data
  };
  
  const logLine = `${timestamp} [${level}] ${message}${data ? ' ' + JSON.stringify(data) : ''}\n`;
  
  // Console output
  console.log(logLine.trim());
  
  // File output
  try {
    fs.appendFileSync(LOG_FILE, logLine);
  } catch (error) {
    console.error('Failed to write to log file:', error.message);
  }
}

function verifySignature(payload, signature) {
  if (!WEBHOOK_SECRET) {
    log('ERROR', 'WEBHOOK_SECRET not configured');
    return false;
  }
  
  if (!signature) {
    log('ERROR', 'No signature provided');
    return false;
  }
  
  const hmac = crypto.createHmac('sha256', WEBHOOK_SECRET);
  const digest = 'sha256=' + hmac.update(payload).digest('hex');
  
  return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(digest));
}

function isRateLimited() {
  const oneHourAgo = Date.now() - (60 * 60 * 1000);
  
  // Clean old entries
  const recentDeployments = deploymentHistory.filter(time => time > oneHourAgo);
  deploymentHistory.length = 0;
  deploymentHistory.push(...recentDeployments);
  
  return recentDeployments.length >= MAX_DEPLOYMENTS_PER_HOUR;
}

function recordDeployment() {
  deploymentHistory.push(Date.now());
}

function executeDeploy() {
  return new Promise((resolve, reject) => {
    log('INFO', 'Starting deployment execution');
    
    const deployCommand = `${DEPLOY_SCRIPT} deploy`;
    
    exec(deployCommand, { 
      timeout: 300000, // 5 minutes timeout
      cwd: path.dirname(DEPLOY_SCRIPT)
    }, (error, stdout, stderr) => {
      if (error) {
        log('ERROR', 'Deployment failed', {
          error: error.message,
          code: error.code,
          stderr: stderr
        });
        reject(error);
      } else {
        log('SUCCESS', 'Deployment completed successfully', {
          stdout: stdout.trim()
        });
        resolve(stdout);
      }
    });
  });
}

function validatePayload(payload) {
  try {
    const data = JSON.parse(payload);
    
    // Check if it's a push event
    if (!data.ref) {
      return { valid: false, reason: 'Not a push event' };
    }
    
    // Check branch
    const branch = data.ref.replace('refs/heads/', '');
    if (branch !== ALLOWED_BRANCH) {
      return { 
        valid: false, 
        reason: `Push to '${branch}' branch, only '${ALLOWED_BRANCH}' triggers deployment` 
      };
    }
    
    // Check if there are actual commits
    if (!data.commits || data.commits.length === 0) {
      return { valid: false, reason: 'No commits in push' };
    }
    
    return { 
      valid: true, 
      data: {
        branch,
        commits: data.commits.length,
        pusher: data.pusher?.name || 'unknown',
        repository: data.repository?.full_name || 'unknown'
      }
    };
    
  } catch (error) {
    return { valid: false, reason: `Invalid JSON payload: ${error.message}` };
  }
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Hub-Signature-256');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Only handle POST requests to /webhook
  if (req.method !== 'POST' || req.url !== '/webhook') {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      error: 'Not Found',
      message: 'Webhook endpoint not found'
    }));
    return;
  }
  
  let body = '';
  
  req.on('data', chunk => {
    body += chunk.toString();
  });
  
  req.on('end', async () => {
    try {
      const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
      const userAgent = req.headers['user-agent'] || 'unknown';
      
      log('INFO', 'Webhook request received', {
        ip: clientIP,
        userAgent: userAgent.substring(0, 100)
      });
      
      // Verify signature
      const signature = req.headers['x-hub-signature-256'];
      if (!verifySignature(body, signature)) {
        log('ERROR', 'Invalid webhook signature', { ip: clientIP });
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
          error: 'Unauthorized',
          message: 'Invalid signature'
        }));
        return;
      }
      
      // Validate payload
      const validation = validatePayload(body);
      if (!validation.valid) {
        log('INFO', 'Webhook ignored', { reason: validation.reason, ip: clientIP });
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
          message: 'Webhook received but ignored',
          reason: validation.reason
        }));
        return;
      }
      
      // Check rate limiting
      if (isRateLimited()) {
        log('WARNING', 'Deployment rate limited', { ip: clientIP });
        res.writeHead(429, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
          error: 'Too Many Requests',
          message: 'Deployment rate limit exceeded'
        }));
        return;
      }
      
      // Record deployment attempt
      recordDeployment();
      
      log('INFO', 'Starting deployment', {
        ...validation.data,
        ip: clientIP
      });
      
      // Execute deployment
      try {
        await executeDeploy();
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
          message: 'Deployment successful',
          timestamp: new Date().toISOString()
        }));
        
      } catch (error) {
        log('ERROR', 'Deployment execution failed', {
          error: error.message,
          ip: clientIP
        });
        
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
          error: 'Deployment Failed',
          message: 'Deployment execution failed'
        }));
      }
      
    } catch (error) {
      log('ERROR', 'Webhook processing error', {
        error: error.message,
        stack: error.stack
      });
      
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ 
        error: 'Internal Server Error',
        message: 'Webhook processing failed'
      }));
    }
  });
  
  // Handle request timeout
  req.on('timeout', () => {
    log('ERROR', 'Webhook request timeout');
    res.writeHead(408, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      error: 'Request Timeout',
      message: 'Request took too long'
    }));
  });
});

// Error handling
server.on('error', (error) => {
  log('ERROR', 'Server error', {
    error: error.message,
    code: error.code
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  log('INFO', 'Received SIGTERM, shutting down gracefully');
  server.close(() => {
    log('INFO', 'Webhook handler stopped');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  log('INFO', 'Received SIGINT, shutting down gracefully');
  server.close(() => {
    log('INFO', 'Webhook handler stopped');
    process.exit(0);
  });
});

// Start server
server.listen(WEBHOOK_PORT, () => {
  log('INFO', 'Webhook handler started', {
    port: WEBHOOK_PORT,
    deployScript: DEPLOY_SCRIPT,
    allowedBranch: ALLOWED_BRANCH,
    rateLimit: `${MAX_DEPLOYMENTS_PER_HOUR}/hour`
  });
  
  console.log(`🚀 Webhook handler listening on port ${WEBHOOK_PORT}`);
  console.log(`📂 Deploy script: ${DEPLOY_SCRIPT}`);
  console.log(`🌿 Allowed branch: ${ALLOWED_BRANCH}`);
  console.log(`⏱️  Rate limit: ${MAX_DEPLOYMENTS_PER_HOUR} deployments/hour`);
  console.log(`📝 Log file: ${LOG_FILE}`);
});

// Health check endpoint
server.on('request', (req, res) => {
  if (req.method === 'GET' && req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      deployments: deploymentHistory.length
    }));
  }
});
