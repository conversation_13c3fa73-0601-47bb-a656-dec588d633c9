/**
 * Authentication Utilities
 * Secure client-side authentication logic with protection against common vulnerabilities
 */

import {
  LoginCredentials,
  AuthUser,
  SessionData,
  LoginAttempt,
  AuthError,
  AuthEventLog,
  AuthEvent
} from '@/types/auth';
import { AUTH_CONFIG, SECURITY_CONFIG, ENCRYPTION_KEY } from '@/config/auth';
const SESSION_STORAGE_KEY = 'streamdb_auth_session';
const LOGIN_ATTEMPTS_KEY = 'streamdb_login_attempts';

/**
 * Simple XOR encryption for session data
 * Note: This is basic client-side obfuscation. Real security comes from server-side validation.
 */
function encryptData(data: string): string {
  let encrypted = '';
  for (let i = 0; i < data.length; i++) {
    encrypted += String.fromCharCode(
      data.charCodeAt(i) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length)
    );
  }
  return btoa(encrypted); // Base64 encode
}

function decryptData(encryptedData: string): string {
  try {
    const encrypted = atob(encryptedData); // Base64 decode
    let decrypted = '';
    for (let i = 0; i < encrypted.length; i++) {
      decrypted += String.fromCharCode(
        encrypted.charCodeAt(i) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length)
      );
    }
    return decrypted;
  } catch (error) {
    console.error('Failed to decrypt session data:', error);
    return '';
  }
}

/**
 * Secure session storage utilities
 */
export class SecureSessionStorage {
  /**
   * Store session data securely
   */
  static setSession(sessionData: SessionData): void {
    try {
      const dataString = JSON.stringify(sessionData);
      const encryptedData = encryptData(dataString);
      sessionStorage.setItem(SESSION_STORAGE_KEY, encryptedData);
    } catch (error) {
      console.error('Failed to store session data:', error);
    }
  }

  /**
   * Retrieve session data securely
   */
  static getSession(): SessionData | null {
    try {
      const encryptedData = sessionStorage.getItem(SESSION_STORAGE_KEY);
      if (!encryptedData) return null;

      const decryptedData = decryptData(encryptedData);
      if (!decryptedData) return null;

      const sessionData: SessionData = JSON.parse(decryptedData);
      
      // Check if session is expired
      if (Date.now() > sessionData.expiresAt) {
        this.clearSession();
        return null;
      }

      return sessionData;
    } catch (error) {
      console.error('Failed to retrieve session data:', error);
      this.clearSession();
      return null;
    }
  }

  /**
   * Clear session data
   */
  static clearSession(): void {
    sessionStorage.removeItem(SESSION_STORAGE_KEY);
  }

  /**
   * Check if session exists and is valid
   */
  static isSessionValid(): boolean {
    const session = this.getSession();
    return session !== null && Date.now() < session.expiresAt;
  }

  /**
   * Get time until session expiry in milliseconds
   */
  static getTimeUntilExpiry(): number {
    const session = this.getSession();
    if (!session) return 0;
    return Math.max(0, session.expiresAt - Date.now());
  }
}

/**
 * Login attempt tracking for brute force protection
 */
export class LoginAttemptTracker {
  /**
   * Record a login attempt
   */
  static recordAttempt(success: boolean): void {
    try {
      const attempts = this.getAttempts();
      const newAttempt: LoginAttempt = {
        timestamp: Date.now(),
        success,
        userAgent: navigator.userAgent,
      };

      attempts.push(newAttempt);

      // Keep only recent attempts (last hour)
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      const recentAttempts = attempts.filter(attempt => attempt.timestamp > oneHourAgo);

      localStorage.setItem(LOGIN_ATTEMPTS_KEY, JSON.stringify(recentAttempts));
    } catch (error) {
      console.error('Failed to record login attempt:', error);
    }
  }

  /**
   * Get recent login attempts
   */
  static getAttempts(): LoginAttempt[] {
    try {
      const attemptsData = localStorage.getItem(LOGIN_ATTEMPTS_KEY);
      return attemptsData ? JSON.parse(attemptsData) : [];
    } catch (error) {
      console.error('Failed to retrieve login attempts:', error);
      return [];
    }
  }

  /**
   * Check if account should be locked due to too many failed attempts
   */
  static isAccountLocked(): boolean {
    const attempts = this.getAttempts();
    const recentFailedAttempts = attempts.filter(
      attempt => !attempt.success &&
      attempt.timestamp > (Date.now() - AUTH_CONFIG.lockoutDuration)
    );

    return recentFailedAttempts.length >= AUTH_CONFIG.maxLoginAttempts;
  }

  /**
   * Get time until account unlock in milliseconds
   */
  static getTimeUntilUnlock(): number {
    if (!this.isAccountLocked()) return 0;

    const attempts = this.getAttempts();
    const failedAttempts = attempts.filter(attempt => !attempt.success);
    
    if (failedAttempts.length === 0) return 0;

    const lastFailedAttempt = failedAttempts[failedAttempts.length - 1];
    const unlockTime = lastFailedAttempt.timestamp + AUTH_CONFIG.lockoutDuration;
    
    return Math.max(0, unlockTime - Date.now());
  }

  /**
   * Clear login attempts (used after successful login)
   */
  static clearAttempts(): void {
    localStorage.removeItem(LOGIN_ATTEMPTS_KEY);
  }
}

/**
 * Input validation and sanitization
 */
export class AuthValidator {
  /**
   * Validate login credentials
   */
  static validateCredentials(credentials: LoginCredentials): { valid: boolean; error?: string } {
    if (!credentials.username || !credentials.password) {
      return { valid: false, error: 'Username and password are required' };
    }

    if (credentials.username.length < 3) {
      return { valid: false, error: 'Username must be at least 3 characters long' };
    }

    if (credentials.password.length < 6) {
      return { valid: false, error: 'Password must be at least 6 characters long' };
    }

    // Check for potential XSS attempts
    if (this.containsXSS(credentials.username) || this.containsXSS(credentials.password)) {
      return { valid: false, error: 'Invalid characters detected' };
    }

    return { valid: true };
  }

  /**
   * Basic XSS detection
   */
  private static containsXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe/gi,
      /<object/gi,
      /<embed/gi,
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Sanitize input string
   */
  static sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }
}

/**
 * Security event logging
 */
export class SecurityLogger {
  private static readonly LOG_KEY = 'streamdb_security_logs';
  private static readonly MAX_LOGS = 100;

  /**
   * Log a security event
   */
  static logEvent(event: AuthEvent, details?: Record<string, any>, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'): void {
    try {
      const logs = this.getLogs();
      const logEntry: AuthEventLog = {
        event,
        timestamp: Date.now(),
        details,
        severity,
      };

      logs.push(logEntry);

      // Keep only recent logs
      if (logs.length > this.MAX_LOGS) {
        logs.splice(0, logs.length - this.MAX_LOGS);
      }

      localStorage.setItem(this.LOG_KEY, JSON.stringify(logs));
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  /**
   * Get security logs
   */
  static getLogs(): AuthEventLog[] {
    try {
      const logsData = localStorage.getItem(this.LOG_KEY);
      return logsData ? JSON.parse(logsData) : [];
    } catch (error) {
      console.error('Failed to retrieve security logs:', error);
      return [];
    }
  }

  /**
   * Clear security logs
   */
  static clearLogs(): void {
    localStorage.removeItem(this.LOG_KEY);
  }
}

/**
 * Generate secure random token
 */
export function generateSecureToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Hash password (basic client-side hashing - server should do proper hashing)
 */
export function hashPassword(password: string): string {
  // This is a simple hash for client-side use only
  // Real password hashing should be done server-side with proper salt and algorithms
  let hash = 0;
  for (let i = 0; i < password.length; i++) {
    const char = password.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(16);
}
