import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Shield,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Loader2,
  Bug,
  Server,
  Globe,
  Clock,
  Info
} from 'lucide-react';
import {
  detectAdBlockers,
  quickAdBlockerCheck,
  testAdvancedAdBlockerDetection,
  AdBlockerDetectionResult
} from '@/utils/adBlockerDetection';
import {
  shouldShowAwarenessPopup,
  getTrackingStats,
  getNextShowTime
} from '@/utils/adBlockerAwarenessTracking';

const ProductionDiagnostics: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [diagnosticResults, setDiagnosticResults] = useState<any>(null);
  const [detectionResult, setDetectionResult] = useState<AdBlockerDetectionResult | null>(null);
  const [environmentInfo, setEnvironmentInfo] = useState<any>(null);

  useEffect(() => {
    // Gather environment information on load
    const envInfo = {
      isProduction: import.meta.env.PROD,
      isDevelopment: import.meta.env.DEV,
      mode: import.meta.env.MODE,
      baseUrl: import.meta.env.BASE_URL,
      userAgent: navigator.userAgent,
      location: {
        href: window.location.href,
        origin: window.location.origin,
        pathname: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash
      },
      screen: {
        width: window.screen.width,
        height: window.screen.height,
        availWidth: window.screen.availWidth,
        availHeight: window.screen.availHeight
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      timestamp: new Date().toISOString(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
    
    setEnvironmentInfo(envInfo);
    console.log('🔧 [DIAGNOSTICS] Environment info:', envInfo);
  }, []);

  const runComprehensiveDiagnostics = async () => {
    setIsRunning(true);
    console.log('🔧 [DIAGNOSTICS] Starting comprehensive diagnostics...');
    
    const results: any = {
      timestamp: new Date().toISOString(),
      environment: environmentInfo,
      tests: {}
    };

    try {
      // Test 1: Quick ad blocker check
      console.log('🔧 [DIAGNOSTICS] Running quick ad blocker check...');
      const quickStart = performance.now();
      const quickResult = await quickAdBlockerCheck();
      const quickDuration = performance.now() - quickStart;
      
      results.tests.quickCheck = {
        result: quickResult,
        duration: quickDuration,
        status: 'completed'
      };

      // Test 2: Full ad blocker detection
      console.log('🔧 [DIAGNOSTICS] Running full ad blocker detection...');
      const fullStart = performance.now();
      const fullResult = await detectAdBlockers();
      const fullDuration = performance.now() - fullStart;
      
      results.tests.fullDetection = {
        result: fullResult,
        duration: fullDuration,
        status: 'completed'
      };
      
      setDetectionResult(fullResult);

      // Test 3: Awareness popup tracking
      console.log('🔧 [DIAGNOSTICS] Checking awareness popup tracking...');
      const shouldShow = shouldShowAwarenessPopup();
      const trackingStats = getTrackingStats();
      const nextShowTime = getNextShowTime();
      
      results.tests.awarenessTracking = {
        shouldShow,
        trackingStats,
        nextShowTime,
        status: 'completed'
      };

      // Test 4: DOM and CSS checks
      console.log('🔧 [DIAGNOSTICS] Running DOM and CSS checks...');
      const domChecks = {
        documentReady: document.readyState,
        bodyExists: !!document.body,
        headExists: !!document.head,
        styleSheetsCount: document.styleSheets.length,
        scriptsCount: document.scripts.length,
        hasAdBlockerElements: !!document.querySelector('[data-adblock], [data-ublock], .adblock-detected'),
        consoleErrors: []
      };
      
      results.tests.domChecks = {
        result: domChecks,
        status: 'completed'
      };

      // Test 5: Advanced ad blocker detection (production only)
      if (environmentInfo?.isProduction) {
        console.log('🔧 [DIAGNOSTICS] Running advanced ad blocker detection...');
        const advancedStart = performance.now();
        try {
          const advancedResult = await testAdvancedAdBlockerDetection();
          const advancedDuration = performance.now() - advancedStart;

          results.tests.advancedDetection = {
            result: advancedResult,
            duration: advancedDuration,
            status: 'completed'
          };
        } catch (error) {
          const advancedDuration = performance.now() - advancedStart;
          results.tests.advancedDetection = {
            error: error instanceof Error ? error.message : 'Unknown error',
            duration: advancedDuration,
            status: 'failed'
          };
        }
      }

      // Test 6: Network connectivity
      console.log('🔧 [DIAGNOSTICS] Testing network connectivity...');
      const networkStart = performance.now();
      try {
        const response = await fetch('https://httpbin.org/get', {
          method: 'GET',
          mode: 'cors'
        });
        const networkDuration = performance.now() - networkStart;

        results.tests.networkConnectivity = {
          result: {
            status: response.status,
            ok: response.ok,
            headers: Object.fromEntries(response.headers.entries())
          },
          duration: networkDuration,
          status: 'completed'
        };
      } catch (error) {
        const networkDuration = performance.now() - networkStart;
        results.tests.networkConnectivity = {
          error: error instanceof Error ? error.message : 'Unknown error',
          duration: networkDuration,
          status: 'failed'
        };
      }

      console.log('🔧 [DIAGNOSTICS] All tests completed:', results);
      setDiagnosticResults(results);

    } catch (error) {
      console.error('🔧 [DIAGNOSTICS] Diagnostic failed:', error);
      results.error = error instanceof Error ? error.message : 'Unknown error';
      setDiagnosticResults(results);
    } finally {
      setIsRunning(false);
    }
  };

  const exportResults = () => {
    if (!diagnosticResults) return;
    
    const dataStr = JSON.stringify(diagnosticResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `production-diagnostics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-primary">Production Diagnostics</h1>
          <p className="text-muted-foreground">
            Comprehensive diagnostic tool for debugging ad blocker detection issues on Vercel
          </p>
        </div>

        {/* Environment Info */}
        {environmentInfo && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Environment Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong>Environment:</strong> {environmentInfo.isProduction ? 'Production' : 'Development'}
                </div>
                <div>
                  <strong>Mode:</strong> {environmentInfo.mode}
                </div>
                <div>
                  <strong>Origin:</strong> {environmentInfo.location.origin}
                </div>
                <div>
                  <strong>User Agent:</strong> {environmentInfo.userAgent.substring(0, 50)}...
                </div>
                <div>
                  <strong>Viewport:</strong> {environmentInfo.viewport.width}x{environmentInfo.viewport.height}
                </div>
                <div>
                  <strong>Timezone:</strong> {environmentInfo.timezone}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bug className="h-5 w-5" />
              Diagnostic Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Button 
                onClick={runComprehensiveDiagnostics}
                disabled={isRunning}
                className="flex items-center gap-2"
              >
                {isRunning ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                {isRunning ? 'Running Diagnostics...' : 'Run Comprehensive Diagnostics'}
              </Button>
              
              {diagnosticResults && (
                <Button 
                  onClick={exportResults}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Globe className="h-4 w-4" />
                  Export Results
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        {diagnosticResults && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Diagnostic Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(diagnosticResults.tests || {}).map(([testName, testResult]: [string, any]) => (
                  <div key={testName} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold capitalize">{testName.replace(/([A-Z])/g, ' $1')}</h3>
                      <Badge variant={testResult.status === 'completed' ? 'default' : 'destructive'}>
                        {testResult.status}
                      </Badge>
                    </div>
                    <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                      {JSON.stringify(testResult, null, 2)}
                    </pre>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Ad Blocker Detection Result */}
        {detectionResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Ad Blocker Detection Result
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Alert className={detectionResult.hasAdBlocker ? "border-orange-500" : "border-green-500"}>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {detectionResult.hasAdBlocker 
                    ? `Ad blocker detected with ${detectionResult.totalConfidence.toFixed(1)}% confidence`
                    : 'No ad blocker detected'
                  }
                </AlertDescription>
              </Alert>
              
              {detectionResult.detectedBlockers.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-semibold mb-2">Detected Blockers:</h4>
                  <div className="space-y-2">
                    {detectionResult.detectedBlockers.map((blocker, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                        <span>{blocker.name}</span>
                        <Badge>{blocker.confidence}% confidence</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ProductionDiagnostics;
