# 🚀 SECURE AUTO-DEPLOYMENT SETUP

## 🎯 GOAL: Automatic Website Updates from GitHub

When you push code to GitHub → Your Alexhost server automatically updates → Website shows new changes

## 📋 STEP-BY-STEP SETUP

### PHASE 1: SERVER PREPARATION

#### Step 1.1: Connect to Your Alexhost Server
```bash
# SSH into your server (use FastPanel SSH or terminal)
ssh your-username@your-server-ip
```

#### Step 1.2: Install Required Tools
```bash
# Install Git (if not already installed)
sudo apt update
sudo apt install git nodejs npm

# Install PM2 for process management
sudo npm install -g pm2

# Install webhook handler
sudo npm install -g github-webhook-handler
```

#### Step 1.3: Create Project Directory
```bash
# Create your website directory
sudo mkdir -p /var/www/your-website
sudo chown $USER:$USER /var/www/your-website
cd /var/www/your-website
```

### PHASE 2: GITHUB REPOSITORY SETUP

#### Step 2.1: Initialize Git Repository
```bash
# In your project directory
git init
git remote add origin https://github.com/yourusername/your-repo.git

# Create .gitignore file
cat > .gitignore << EOF
# Environment files (NEVER commit these)
.env
.env.local
.env.production

# Node modules
node_modules/
npm-debug.log*

# Build files
dist/
build/

# Logs
logs/
*.log

# Database files
*.db
*.sqlite

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/

# Temporary files
tmp/
temp/
uploads/temp/

# Backup files
*.backup
*.bak
EOF
```

#### Step 2.2: Secure Environment Setup
```bash
# Create secure environment file (NEVER commit this)
cat > .env << EOF
# Database Configuration - GET FROM FASTPANEL
DB_HOST=localhost
DB_PORT=3306
DB_NAME=streamdb_database
DB_USER=streamdb_opl_user
DB_PASSWORD=YOUR_ACTUAL_PASSWORD_HERE

# Security Keys - GENERATE THESE
JWT_SECRET=$(node -e "console.log(require('crypto').randomBytes(64).toString('hex'))")
SESSION_SECRET=$(node -e "console.log(require('crypto').randomBytes(64).toString('hex'))")

# Server Configuration
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://your-domain.com

# Webhook Secret - GENERATE THIS
WEBHOOK_SECRET=$(node -e "console.log(require('crypto').randomBytes(32).toString('hex'))")
EOF

# Make environment file read-only
chmod 600 .env
```

### PHASE 3: DEPLOYMENT SCRIPT CREATION

#### Step 3.1: Create Deployment Script
```bash
# Create deployment script
cat > deploy.sh << 'EOF'
#!/bin/bash

# Secure Auto-Deployment Script
set -e  # Exit on any error

# Configuration
PROJECT_DIR="/var/www/your-website"
BACKUP_DIR="/var/backups/website"
LOG_FILE="/var/log/deploy.log"
REPO_URL="https://github.com/yourusername/your-repo.git"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

# Function to create backup
create_backup() {
    log_message "Creating backup..."
    mkdir -p $BACKUP_DIR
    BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
    cp -r $PROJECT_DIR $BACKUP_DIR/$BACKUP_NAME
    log_message "Backup created: $BACKUP_DIR/$BACKUP_NAME"
}

# Function to deploy
deploy() {
    log_message "Starting deployment..."
    
    cd $PROJECT_DIR
    
    # Pull latest changes
    log_message "Pulling latest changes from GitHub..."
    git fetch origin
    git reset --hard origin/main
    
    # Install/update dependencies
    if [ -f "package.json" ]; then
        log_message "Installing dependencies..."
        npm install --production
    fi
    
    if [ -f "server/package.json" ]; then
        log_message "Installing server dependencies..."
        cd server
        npm install --production
        cd ..
    fi
    
    # Build frontend if needed
    if [ -f "package.json" ] && grep -q "build" package.json; then
        log_message "Building frontend..."
        npm run build
    fi
    
    # Restart services
    log_message "Restarting services..."
    pm2 restart streaming-db-api || pm2 start server/index.js --name streaming-db-api
    
    # Restart web server if needed
    if command -v nginx &> /dev/null; then
        sudo systemctl reload nginx
    fi
    
    log_message "Deployment completed successfully!"
}

# Function to rollback
rollback() {
    log_message "Rolling back to previous version..."
    LATEST_BACKUP=$(ls -t $BACKUP_DIR | head -n1)
    if [ -n "$LATEST_BACKUP" ]; then
        rm -rf $PROJECT_DIR
        cp -r $BACKUP_DIR/$LATEST_BACKUP $PROJECT_DIR
        pm2 restart streaming-db-api
        log_message "Rollback completed!"
    else
        log_message "No backup found for rollback!"
        exit 1
    fi
}

# Main execution
case "$1" in
    "deploy")
        create_backup
        deploy
        ;;
    "rollback")
        rollback
        ;;
    *)
        echo "Usage: $0 {deploy|rollback}"
        exit 1
        ;;
esac
EOF

# Make script executable
chmod +x deploy.sh
```

#### Step 3.2: Create Webhook Handler
```bash
# Create webhook handler script
cat > webhook-handler.js << 'EOF'
const http = require('http');
const crypto = require('crypto');
const { exec } = require('child_process');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;
const PORT = process.env.WEBHOOK_PORT || 9000;
const DEPLOY_SCRIPT = '/var/www/your-website/deploy.sh';

// Verify GitHub webhook signature
function verifySignature(payload, signature) {
    const hmac = crypto.createHmac('sha256', WEBHOOK_SECRET);
    const digest = 'sha256=' + hmac.update(payload).digest('hex');
    return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(digest));
}

// Execute deployment
function executeDeploy() {
    return new Promise((resolve, reject) => {
        exec(`${DEPLOY_SCRIPT} deploy`, (error, stdout, stderr) => {
            if (error) {
                console.error('Deployment failed:', error);
                reject(error);
            } else {
                console.log('Deployment output:', stdout);
                resolve(stdout);
            }
        });
    });
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
    if (req.method === 'POST' && req.url === '/webhook') {
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', async () => {
            try {
                const signature = req.headers['x-hub-signature-256'];
                
                if (!signature || !verifySignature(body, signature)) {
                    res.writeHead(401, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Unauthorized' }));
                    return;
                }
                
                const payload = JSON.parse(body);
                
                // Only deploy on push to main branch
                if (payload.ref === 'refs/heads/main') {
                    console.log('Received push to main branch, starting deployment...');
                    
                    try {
                        await executeDeploy();
                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ message: 'Deployment successful' }));
                    } catch (error) {
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Deployment failed' }));
                    }
                } else {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ message: 'Not main branch, skipping deployment' }));
                }
                
            } catch (error) {
                console.error('Webhook error:', error);
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Bad request' }));
            }
        });
    } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not found' }));
    }
});

server.listen(PORT, () => {
    console.log(`Webhook handler listening on port ${PORT}`);
});
EOF
```

### PHASE 4: GITHUB WEBHOOK CONFIGURATION

#### Step 4.1: Start Webhook Handler
```bash
# Start webhook handler with PM2
pm2 start webhook-handler.js --name webhook-handler

# Save PM2 configuration
pm2 save
pm2 startup
```

#### Step 4.2: Configure GitHub Webhook
1. **Go to your GitHub repository**
2. **Click Settings → Webhooks → Add webhook**
3. **Configure webhook:**
   - **Payload URL**: `https://your-domain.com:9000/webhook`
   - **Content type**: `application/json`
   - **Secret**: Use the WEBHOOK_SECRET from your .env file
   - **Events**: Select "Just the push event"
   - **Active**: ✅ Checked

### PHASE 5: NGINX CONFIGURATION (If using Nginx)

#### Step 5.1: Configure Nginx for Webhook
```bash
# Add to your Nginx configuration
sudo nano /etc/nginx/sites-available/your-website
```

```nginx
# Add this location block to your existing server configuration
location /webhook {
    proxy_pass http://localhost:9000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}
```

```bash
# Test and reload Nginx
sudo nginx -t
sudo systemctl reload nginx
```

### PHASE 6: TESTING THE SETUP

#### Step 6.1: Test Manual Deployment
```bash
# Test the deployment script
./deploy.sh deploy

# Check if services are running
pm2 status
```

#### Step 6.2: Test GitHub Integration
1. **Make a small change** to your code
2. **Commit and push** to main branch:
   ```bash
   git add .
   git commit -m "Test auto-deployment"
   git push origin main
   ```
3. **Check deployment logs**:
   ```bash
   tail -f /var/log/deploy.log
   pm2 logs webhook-handler
   ```

## 🔒 SECURITY FEATURES

✅ **Webhook signature verification**  
✅ **Environment variables protected**  
✅ **Automatic backups before deployment**  
✅ **Rollback capability**  
✅ **Secure file permissions**  
✅ **Process isolation with PM2**  

## 🚨 IMPORTANT SECURITY NOTES

1. **NEVER commit .env files** to GitHub
2. **Use strong webhook secrets**
3. **Regularly rotate secrets**
4. **Monitor deployment logs**
5. **Test rollback procedures**

## 🛠️ MAINTENANCE COMMANDS

```bash
# View deployment logs
tail -f /var/log/deploy.log

# Check webhook handler status
pm2 status webhook-handler

# Manual deployment
./deploy.sh deploy

# Rollback to previous version
./deploy.sh rollback

# Restart services
pm2 restart all
```

## ✅ SETUP CHECKLIST

- [ ] Server tools installed (Git, Node.js, PM2)
- [ ] Project directory created
- [ ] .gitignore configured
- [ ] .env file created (with secure keys)
- [ ] Deployment script created and tested
- [ ] Webhook handler configured
- [ ] GitHub webhook configured
- [ ] Nginx configured (if applicable)
- [ ] Manual deployment tested
- [ ] Auto-deployment tested
- [ ] Rollback tested

**🎉 Once complete, your website will automatically update whenever you push to GitHub!**
