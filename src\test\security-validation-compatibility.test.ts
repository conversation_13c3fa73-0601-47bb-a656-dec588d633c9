/**
 * Test security encoding compatibility with enhanced validation
 * Ensures validation and security encoding work together properly
 */

import { 
  isValidVideoLink, 
  isSecureVideoLink, 
  encodeVideoLinks, 
  decodeVideoLinks, 
  parseVideoLinks,
  extractVideoUrl 
} from '../utils/videoSecurity';

// Test the user's specific 2embed.cc link
const userLink = 'https://www.2embed.cc/movie/574475';

// Test multiple 2embed.cc formats
const test2EmbedLinks = `https://www.2embed.cc/movie/574475
https://www.2embed.cc/embed/574475
https://2embed.cc/movie/123456
//www.2embed.cc/embed/789012`;

/**
 * Test the complete workflow: validation → encoding → decoding → extraction
 */
export function testSecurityValidationWorkflow(): boolean {
  console.group('🔒 Security + Validation Compatibility Test');
  
  let allPassed = true;
  
  // Step 1: Test individual link validation
  console.log('Step 1: Testing user link validation');
  const isValid = isValidVideoLink(userLink);
  const isSecure = isSecureVideoLink(userLink);
  
  console.log(`  Link: ${userLink}`);
  console.log(`  Valid: ${isValid ? '✅' : '❌'}`);
  console.log(`  Secure: ${isSecure ? '✅' : '❌'}`);
  
  if (!isValid || !isSecure) {
    console.error('❌ User link failed validation or security check');
    allPassed = false;
  }
  
  // Step 2: Test encoding
  console.log('\nStep 2: Testing encoding');
  const encoded = encodeVideoLinks(test2EmbedLinks);
  console.log(`  Encoded successfully: ${encoded ? '✅' : '❌'}`);
  
  if (!encoded) {
    console.error('❌ Encoding failed');
    allPassed = false;
  }
  
  // Step 3: Test decoding
  console.log('\nStep 3: Testing decoding');
  const decoded = decodeVideoLinks(encoded);
  const decodingWorked = decoded === test2EmbedLinks;
  console.log(`  Decoded successfully: ${decodingWorked ? '✅' : '❌'}`);
  
  if (!decodingWorked) {
    console.error('❌ Decoding failed or data corrupted');
    console.error('  Original:', test2EmbedLinks);
    console.error('  Decoded:', decoded);
    allPassed = false;
  }
  
  // Step 4: Test parsing after decoding
  console.log('\nStep 4: Testing parsing after decoding');
  const parsed = parseVideoLinks(decoded);
  console.log(`  Parsed ${parsed.length} links`);
  
  parsed.forEach((link, index) => {
    const valid = isValidVideoLink(link);
    const secure = isSecureVideoLink(link);
    console.log(`  Link ${index + 1}: ${valid && secure ? '✅' : '❌'} ${link.substring(0, 40)}...`);
    
    if (!valid || !secure) {
      allPassed = false;
    }
  });
  
  // Step 5: Test URL extraction
  console.log('\nStep 5: Testing URL extraction');
  parsed.forEach((link, index) => {
    const extracted = extractVideoUrl(link);
    console.log(`  Extracted ${index + 1}: ${extracted}`);
    
    // Check if 2embed.cc movie URLs were transformed to embed URLs
    if (link.includes('2embed.cc/movie/') && !extracted.includes('/embed/')) {
      console.error(`❌ URL transformation failed for: ${link}`);
      allPassed = false;
    }
  });
  
  // Step 6: Overall workflow test
  console.log('\nStep 6: Complete workflow test');
  console.log(`  Overall result: ${allPassed ? '✅ ALL PASSED' : '❌ SOME FAILED'}`);
  
  console.groupEnd();
  
  return allPassed;
}

/**
 * Test that validation doesn't break existing security features
 */
export function testSecurityFeatureIntegrity(): boolean {
  console.group('🛡️ Security Feature Integrity Test');
  
  // Test that dangerous patterns are still blocked
  const dangerousLinks = [
    'javascript:alert("xss")',
    'data:text/html,<script>alert("xss")</script>',
    '<script>alert("xss")</script>',
    'https://example.com/embed/test?onload=alert("xss")',
  ];
  
  let allBlocked = true;
  
  dangerousLinks.forEach((link, index) => {
    const isSecure = isSecureVideoLink(link);
    console.log(`  Dangerous link ${index + 1}: ${isSecure ? '❌ NOT BLOCKED' : '✅ BLOCKED'}`);
    
    if (isSecure) {
      console.error(`    Should have been blocked: ${link}`);
      allBlocked = false;
    }
  });
  
  console.log(`\nSecurity integrity: ${allBlocked ? '✅ MAINTAINED' : '❌ COMPROMISED'}`);
  console.groupEnd();
  
  return allBlocked;
}

/**
 * Test specific 2embed.cc scenarios
 */
export function test2EmbedScenarios(): boolean {
  console.group('🎬 2embed.cc Specific Scenarios');
  
  const scenarios = [
    {
      name: 'User original link',
      link: 'https://www.2embed.cc/movie/574475',
      shouldValidate: true,
      shouldTransform: true
    },
    {
      name: 'Proper embed link',
      link: 'https://www.2embed.cc/embed/574475',
      shouldValidate: true,
      shouldTransform: false
    },
    {
      name: 'Alternative domain',
      link: 'https://2embed.to/movie/123456',
      shouldValidate: true,
      shouldTransform: true
    },
    {
      name: 'Protocol-relative',
      link: '//www.2embed.cc/movie/789012',
      shouldValidate: true,
      shouldTransform: true
    }
  ];
  
  let allPassed = true;
  
  scenarios.forEach(({ name, link, shouldValidate, shouldTransform }) => {
    console.log(`\nTesting: ${name}`);
    console.log(`  Link: ${link}`);
    
    const isValid = isValidVideoLink(link);
    const extracted = extractVideoUrl(link);
    const wasTransformed = extracted !== link && extracted.includes('/embed/');
    
    console.log(`  Valid: ${isValid ? '✅' : '❌'} (expected: ${shouldValidate})`);
    console.log(`  Transformed: ${wasTransformed ? '✅' : '❌'} (expected: ${shouldTransform})`);
    console.log(`  Final URL: ${extracted}`);
    
    if (isValid !== shouldValidate || wasTransformed !== shouldTransform) {
      allPassed = false;
    }
  });
  
  console.log(`\n2embed.cc scenarios: ${allPassed ? '✅ ALL PASSED' : '❌ SOME FAILED'}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Run all compatibility tests
 */
export function runSecurityValidationCompatibilityTests(): boolean {
  console.group('🧪 Security + Validation Compatibility Tests');
  
  const results = [
    testSecurityValidationWorkflow(),
    testSecurityFeatureIntegrity(),
    test2EmbedScenarios()
  ];
  
  const allPassed = results.every(Boolean);
  const passedCount = results.filter(Boolean).length;
  
  console.log(`\n🎯 Overall Compatibility: ${allPassed ? '✅ PERFECT' : '⚠️ ISSUES FOUND'}`);
  console.log(`📊 Test Results: ${passedCount}/${results.length} test suites passed`);
  
  if (allPassed) {
    console.log('✅ Validation fixes are fully compatible with security encoding');
    console.log('✅ No conflicts between validation and security systems');
    console.log('✅ 2embed.cc links now work end-to-end');
  }
  
  console.groupEnd();
  
  return allPassed;
}

// Export for console testing
(window as any).testSecurity = {
  testSecurityValidationWorkflow,
  testSecurityFeatureIntegrity,
  test2EmbedScenarios,
  runSecurityValidationCompatibilityTests
};
