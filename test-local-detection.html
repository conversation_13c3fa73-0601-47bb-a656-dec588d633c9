<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Ad Blocker Detection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a0a;
            color: #e6cb8e;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #333;
        }
        .success { background: #1a4a1a; border-color: #4a8a4a; }
        .error { background: #4a1a1a; border-color: #8a4a4a; }
        .info { background: #1a1a4a; border-color: #4a4a8a; }
        button {
            background: #e6cb8e;
            color: #0a0a0a;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #d4b876;
        }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 Local Ad Blocker Detection Test</h1>
    <p>This page tests the ad blocker detection functionality locally before deployment.</p>
    
    <div>
        <button onclick="testElementHiding()">Test Element Hiding</button>
        <button onclick="testScriptBlocking()">Test Script Blocking</button>
        <button onclick="testNetworkBlocking()">Test Network Blocking</button>
        <button onclick="testVercelCompatible()">Test Vercel Compatible</button>
        <button onclick="runFullTest()">Run Full Test</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${JSON.stringify(content, null, 2)}</pre>`;
            results.appendChild(div);
        }
        
        function clearResults() {
            results.innerHTML = '';
        }
        
        async function testElementHiding() {
            console.log('🔍 Testing element hiding...');
            
            const testElements = ['ads', 'advertisement', 'ad-banner', 'google-ads', 'adsystem'];
            let hiddenCount = 0;
            
            const promises = testElements.map((className, index) => {
                return new Promise((resolve) => {
                    const testDiv = document.createElement('div');
                    testDiv.className = className;
                    testDiv.style.cssText = `
                        position: absolute;
                        top: 0px;
                        left: 0px;
                        width: 1px;
                        height: 1px;
                        overflow: hidden;
                        z-index: -1000;
                    `;
                    testDiv.innerHTML = '&nbsp;';
                    
                    document.body.appendChild(testDiv);
                    
                    setTimeout(() => {
                        const computedStyle = window.getComputedStyle(testDiv);
                        const isHidden = computedStyle.display === 'none' ||
                                       computedStyle.visibility === 'hidden' ||
                                       computedStyle.opacity === '0' ||
                                       (testDiv.offsetHeight === 0 && testDiv.offsetWidth === 0);
                        
                        if (isHidden) hiddenCount++;
                        
                        document.body.removeChild(testDiv);
                        resolve({ className, isHidden });
                    }, 150);
                });
            });
            
            const elementResults = await Promise.all(promises);
            const result = hiddenCount / testElements.length >= 0.7;
            
            addResult('Element Hiding Test', {
                hiddenCount,
                totalElements: testElements.length,
                threshold: 0.7,
                result,
                details: elementResults
            }, result ? 'error' : 'success');
        }
        
        async function testScriptBlocking() {
            console.log('🔍 Testing script blocking...');
            
            const testScripts = [
                'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js',
                'https://www.googletagservices.com/tag/js/gpt.js'
            ];
            
            let blockedCount = 0;
            
            const promises = testScripts.map((scriptUrl) => {
                return new Promise((resolve) => {
                    const script = document.createElement('script');
                    script.src = scriptUrl;
                    script.async = true;
                    
                    const timeout = setTimeout(() => {
                        blockedCount++;
                        resolve({ scriptUrl, status: 'timeout/blocked' });
                    }, 2000);
                    
                    script.onload = () => {
                        clearTimeout(timeout);
                        resolve({ scriptUrl, status: 'loaded' });
                    };
                    
                    script.onerror = () => {
                        clearTimeout(timeout);
                        blockedCount++;
                        resolve({ scriptUrl, status: 'error/blocked' });
                    };
                    
                    document.head.appendChild(script);
                    
                    setTimeout(() => {
                        if (script.parentNode) {
                            script.parentNode.removeChild(script);
                        }
                    }, 3000);
                });
            });
            
            const scriptResults = await Promise.all(promises);
            const result = blockedCount > 0;
            
            addResult('Script Blocking Test', {
                blockedCount,
                totalScripts: testScripts.length,
                result,
                details: scriptResults
            }, result ? 'error' : 'success');
        }
        
        async function testNetworkBlocking() {
            console.log('🔍 Testing network blocking...');
            
            const testUrls = [
                'https://googleads.g.doubleclick.net/pagead/ads',
                'https://tpc.googlesyndication.com/simgad'
            ];
            
            let blockedCount = 0;
            
            const promises = testUrls.map((url) => {
                return new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        blockedCount++;
                        resolve({ url, status: 'timeout/blocked' });
                    }, 3000);
                    
                    fetch(url, { method: 'HEAD', mode: 'no-cors' })
                        .then(() => {
                            clearTimeout(timeout);
                            resolve({ url, status: 'success' });
                        })
                        .catch(() => {
                            clearTimeout(timeout);
                            blockedCount++;
                            resolve({ url, status: 'error/blocked' });
                        });
                });
            });
            
            const networkResults = await Promise.all(promises);
            const result = blockedCount > 0;
            
            addResult('Network Blocking Test', {
                blockedCount,
                totalUrls: testUrls.length,
                result,
                details: networkResults
            }, result ? 'error' : 'success');
        }
        
        async function testVercelCompatible() {
            console.log('🔍 Testing Vercel compatible detection...');
            
            let detectionScore = 0;
            const tests = [];
            
            // Test 1: Check for ad blocker globals
            try {
                if (typeof window.uBlock !== 'undefined' || 
                    typeof window.adblock !== 'undefined' ||
                    typeof window.AdBlock !== 'undefined') {
                    detectionScore++;
                    tests.push({ test: 'Global variables', result: 'detected' });
                } else {
                    tests.push({ test: 'Global variables', result: 'not found' });
                }
            } catch (e) {
                tests.push({ test: 'Global variables', result: 'error', error: e.message });
            }
            
            // Test 2: Check for extension markers
            try {
                const hasMarkers = document.querySelector('[data-adblock], [data-ublock], .adblock-detected');
                if (hasMarkers) {
                    detectionScore++;
                    tests.push({ test: 'Extension markers', result: 'detected' });
                } else {
                    tests.push({ test: 'Extension markers', result: 'not found' });
                }
            } catch (e) {
                tests.push({ test: 'Extension markers', result: 'error', error: e.message });
            }
            
            // Test 3: Check for Brave browser
            try {
                if (window.navigator?.brave || navigator.userAgent.includes('brave')) {
                    detectionScore++;
                    tests.push({ test: 'Brave browser', result: 'detected' });
                } else {
                    tests.push({ test: 'Brave browser', result: 'not found' });
                }
            } catch (e) {
                tests.push({ test: 'Brave browser', result: 'error', error: e.message });
            }
            
            const result = detectionScore >= 1;
            
            addResult('Vercel Compatible Test', {
                detectionScore,
                threshold: 1,
                result,
                details: tests
            }, result ? 'error' : 'success');
        }
        
        async function runFullTest() {
            clearResults();
            addResult('Starting Full Test Suite', { timestamp: new Date().toISOString() }, 'info');
            
            await testElementHiding();
            await testScriptBlocking();
            await testNetworkBlocking();
            await testVercelCompatible();
            
            addResult('Full Test Complete', { timestamp: new Date().toISOString() }, 'info');
        }
        
        // Auto-run on page load
        window.addEventListener('load', () => {
            addResult('Page Loaded', {
                userAgent: navigator.userAgent,
                location: window.location.href,
                timestamp: new Date().toISOString()
            }, 'info');
        });
    </script>
</body>
</html>
