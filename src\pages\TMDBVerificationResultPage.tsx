import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Search, Key } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/Header';
import { scrollToTop } from '@/utils/scrollToTop';

interface TMDBVerificationResults {
  results: {
    apiKey: boolean;
    validation: boolean;
    movieFetch: boolean;
    tvFetch: boolean;
    search: boolean;
    imageUrls: boolean;
    errors: string[];
  };
  summary: {
    totalTests: number;
    passedTests: number;
    successRate: number;
    errors: string[];
  };
}

const TMDBVerificationResultPage: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TMDBVerificationResults | null>(null);
  const [testStarted, setTestStarted] = useState(false);

  const runVerification = async () => {
    setIsRunning(true);
    setTestStarted(true);
    
    try {
      const { verifyTMDBIntegration } = await import('@/utils/tmdbVerification');
      const verificationResults = await verifyTMDBIntegration();
      setResults(verificationResults);
    } catch (error) {
      console.error('TMDB verification failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const resetTest = () => {
    setResults(null);
    setTestStarted(false);
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusBadge = (passed: boolean) => {
    return (
      <Badge variant={passed ? "default" : "destructive"} className="ml-2">
        {passed ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  const verificationTests = [
    { key: 'apiKey', label: 'API Key Configuration', icon: '🔑' },
    { key: 'validation', label: 'TMDB ID Validation', icon: '✅' },
    { key: 'movieFetch', label: 'Movie Data Fetching', icon: '🎬' },
    { key: 'tvFetch', label: 'TV Show Data Fetching', icon: '📺' },
    { key: 'search', label: 'Search Functionality', icon: '🔍' },
    { key: 'imageUrls', label: 'Image URL Building', icon: '🖼️' }
  ];

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-primary mb-2">
              TMDB Integration Verification
            </h1>
            <p className="text-muted-foreground">
              Quick verification of TMDB API setup and basic functionality
            </p>
          </div>
          
          <Link to="/admin" onClick={scrollToTop}>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Admin
            </Button>
          </Link>
        </div>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="w-5 h-5" />
              Verification Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={runVerification}
                disabled={isRunning}
                className="flex-1"
              >
                {isRunning ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Verifying TMDB...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Verify TMDB Integration
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={resetTest}
                disabled={isRunning}
                className="flex-1"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reset Test
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testStarted && results && (
          <>
            {/* Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {results.summary.successRate === 100 ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : results.summary.successRate >= 80 ? (
                    <AlertCircle className="w-5 h-5 text-yellow-500" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500" />
                  )}
                  Verification Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-green-500">{results.summary.passedTests}</div>
                    <div className="text-sm text-muted-foreground">Tests Passed</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-red-500">{results.summary.totalTests - results.summary.passedTests}</div>
                    <div className="text-sm text-muted-foreground">Tests Failed</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-primary">{results.summary.successRate}%</div>
                    <div className="text-sm text-muted-foreground">Success Rate</div>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="w-4 h-4" />
                  <AlertDescription>
                    {results.summary.successRate === 100 ? (
                      <span className="text-green-600">🎉 TMDB integration is fully functional!</span>
                    ) : results.summary.successRate >= 80 ? (
                      <span className="text-yellow-600">✅ TMDB integration is mostly working. Minor issues detected.</span>
                    ) : (
                      <span className="text-red-600">⚠️ TMDB integration has significant issues. Please review configuration.</span>
                    )}
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Detailed Verification Results */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  🔍 Verification Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {verificationTests.map((test) => {
                    const passed = results.results[test.key as keyof typeof results.results] as boolean;
                    return (
                      <div key={test.key} className="flex items-center justify-between p-4 bg-muted rounded-lg">
                        <div className="flex items-center gap-3">
                          <span className="text-lg">{test.icon}</span>
                          <div>
                            <div className="font-medium">{test.label}</div>
                            <div className="text-sm text-muted-foreground">
                              {test.key === 'apiKey' && 'Checks if TMDB API key is configured'}
                              {test.key === 'validation' && 'Tests TMDB ID validation functions'}
                              {test.key === 'movieFetch' && 'Fetches sample movie data (Inception)'}
                              {test.key === 'tvFetch' && 'Fetches sample TV show data (Breaking Bad)'}
                              {test.key === 'search' && 'Tests search functionality with sample query'}
                              {test.key === 'imageUrls' && 'Tests image URL building functions'}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(passed)}
                          {getStatusBadge(passed)}
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Error Details */}
                {results.summary.errors.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-medium mb-3 text-red-500">Issues Found:</h4>
                    <div className="space-y-2">
                      {results.summary.errors.map((error, index) => (
                        <Alert key={index} variant="destructive">
                          <XCircle className="w-4 h-4" />
                          <AlertDescription>{error}</AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Verification Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="w-4 h-4" />
              <AlertDescription>
                <strong>How to verify TMDB integration:</strong>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Click "Verify TMDB Integration" to run quick verification checks</li>
                  <li>• This is a lighter test compared to the comprehensive TMDB API tests</li>
                  <li>• Checks API key configuration, basic functionality, and data fetching</li>
                  <li>• Green indicators show working features, red indicators show issues</li>
                  <li>• If API key test fails, check your .env file for VITE_TMDB_API_KEY</li>
                  <li>• For detailed testing, use the "Test TMDB API" button instead</li>
                </ul>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TMDBVerificationResultPage;
