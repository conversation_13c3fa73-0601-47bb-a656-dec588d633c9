#!/usr/bin/env node

/**
 * Simple Database Connection Test
 * Quick test to verify database connectivity
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function testConnection() {
  console.log('🔍 Testing Database Connection...');
  console.log('================================');
  
  // Show configuration (without password)
  console.log('\n📋 Configuration:');
  console.log(`Database: ${process.env.DB_NAME}`);
  console.log(`User: ${process.env.DB_USER}`);
  console.log(`Host: ${process.env.DB_HOST}`);
  console.log(`Socket: ${process.env.DB_SOCKET}`);
  
  try {
    const config = {
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4',
      timezone: '+00:00'
    };

    // Use socket for production, TCP for development
    if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET) {
      config.socketPath = process.env.DB_SOCKET;
      console.log(`\n🔌 Attempting socket connection: ${process.env.DB_SOCKET}`);
    } else {
      config.host = process.env.DB_HOST || 'localhost';
      config.port = process.env.DB_PORT || 3306;
      console.log(`\n🔌 Attempting TCP connection: ${config.host}:${config.port}`);
    }

    console.log('\n⏳ Connecting...');
    const connection = await mysql.createConnection(config);
    
    console.log('✅ Connection successful!');
    
    // Test basic query
    console.log('\n📊 Testing basic query...');
    const [rows] = await connection.execute('SELECT DATABASE() as current_db, VERSION() as version, NOW() as current_time');
    
    console.log('✅ Query successful!');
    console.log(`📍 Current Database: ${rows[0].current_db}`);
    console.log(`🔧 MySQL Version: ${rows[0].version}`);
    console.log(`⏰ Server Time: ${rows[0].current_time}`);
    
    // Test table existence
    console.log('\n🏗️ Checking tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`✅ Found ${tables.length} tables:`);
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`   - ${tableName}`);
    });
    
    // Test content count
    try {
      const [contentCount] = await connection.execute('SELECT COUNT(*) as count FROM content');
      console.log(`\n📚 Content items: ${contentCount[0].count}`);
    } catch (error) {
      console.log(`\n⚠️  Could not count content: ${error.message}`);
    }
    
    // Test categories count
    try {
      const [categoryCount] = await connection.execute('SELECT COUNT(*) as count FROM categories');
      console.log(`📂 Categories: ${categoryCount[0].count}`);
    } catch (error) {
      console.log(`⚠️  Could not count categories: ${error.message}`);
    }
    
    // Test admin users count
    try {
      const [adminCount] = await connection.execute('SELECT COUNT(*) as count FROM admin_users WHERE is_active = 1');
      console.log(`👤 Active admin users: ${adminCount[0].count}`);
    } catch (error) {
      console.log(`⚠️  Could not count admin users: ${error.message}`);
    }
    
    await connection.end();
    console.log('\n🎉 Database connection test PASSED!');
    console.log('✅ Your database is properly connected and accessible.');
    
    return true;
    
  } catch (error) {
    console.log('\n❌ Database connection test FAILED!');
    console.log(`Error: ${error.message}`);
    console.log(`Code: ${error.code}`);
    
    // Provide specific troubleshooting
    if (error.code === 'ENOENT') {
      console.log('\n💡 Troubleshooting:');
      console.log('- Socket file not found. Check if MySQL is running.');
      console.log('- Verify socket path: /var/run/mysqld/mysqld.sock');
      console.log('- Try: sudo systemctl status mysql');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Troubleshooting:');
      console.log('- Check database username and password');
      console.log('- Verify user has access to the database');
      console.log('- Check user permissions in MySQL');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Troubleshooting:');
      console.log('- Database does not exist');
      console.log('- Check database name in FastPanel');
      console.log('- Create database if needed');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Troubleshooting:');
      console.log('- MySQL server is not running');
      console.log('- Check if MySQL service is active');
      console.log('- Verify connection settings');
    }
    
    return false;
  }
}

// Run the test
testConnection().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});
