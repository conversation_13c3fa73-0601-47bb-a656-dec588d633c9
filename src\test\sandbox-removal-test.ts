/**
 * Test file to verify sandbox restrictions have been removed
 * and video playback works correctly across all platforms
 */

import { 
  getIFrameConfig, 
  getIFrameConfigDescription, 
  detectVideoPlatform 
} from '../utils/videoSecurity';
import { validateIframeSrc, getSecureIframeAttributes } from '../utils/securityHeaders';

// Test the user's specific failing links that had sandbox issues
const previouslyFailingLinks = [
  'https://gradehgplus.com/e/xvay1ggua7s7',
  'https://streamtape.com/v/YeRw6amy3MsvWa7/Dont.Leave.2022.720p.WEB-DL.English.ESubs.MoviesMod.com.mkv',
  'https://filemoon.to/e/ezfjmgsjwwsh'
];

// Additional test links for various platforms
const testPlatformLinks = [
  'https://www.youtube.com/embed/dQw4w9WgXcQ',
  'https://player.vimeo.com/video/123456789',
  'https://www.2embed.cc/embed/movie/574475',
  'https://streamable.com/e/abc123'
];

/**
 * Test that sandbox restrictions have been removed
 */
export function testSandboxRemoval(): boolean {
  console.group('🧪 Testing Sandbox Restriction Removal');
  
  let allPassed = true;
  const allTestLinks = [...previouslyFailingLinks, ...testPlatformLinks];
  
  allTestLinks.forEach((link, index) => {
    console.group(`Testing Link ${index + 1}: ${link.substring(0, 50)}...`);
    
    const platform = detectVideoPlatform(link);
    const config = getIFrameConfig(link);
    const secureAttrs = getSecureIframeAttributes(link);
    const description = getIFrameConfigDescription(config);
    
    console.log(`Platform: ${platform}`);
    console.log(`Legacy Config Sandbox: "${config.sandbox}"`);
    console.log(`Secure Attrs Sandbox: "${secureAttrs.sandbox}"`);
    console.log(`Referrer Policy: ${config.referrerPolicy}`);
    console.log(`Description: ${description}`);
    
    // Verify sandbox is empty (no restrictions)
    const hasSandboxRestrictions = config.sandbox && config.sandbox.trim() !== '';
    const secureAttrsSandbox = secureAttrs.sandbox && secureAttrs.sandbox.trim() !== '';
    
    if (hasSandboxRestrictions) {
      console.error(`❌ Legacy config still has sandbox restrictions: "${config.sandbox}"`);
      allPassed = false;
    } else {
      console.log(`✅ Legacy config has no sandbox restrictions`);
    }
    
    if (secureAttrsSandbox) {
      console.error(`❌ Secure attributes have sandbox restrictions: "${secureAttrs.sandbox}"`);
      allPassed = false;
    } else {
      console.log(`✅ Secure attributes have no sandbox restrictions`);
    }
    
    // Verify security validation passes
    const isSecure = validateIframeSrc(link);
    if (!isSecure) {
      console.warn(`⚠️ Security validation failed for: ${link}`);
    } else {
      console.log(`✅ Security validation passed`);
    }
    
    console.groupEnd();
  });
  
  const result = allPassed ? '✅ PASSED' : '❌ FAILED';
  console.log(`\n🎯 Sandbox Removal Test: ${result}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Test enhanced security configuration
 */
export function testEnhancedSecurity(): boolean {
  console.group('🔒 Testing Enhanced Security Configuration');
  
  let allPassed = true;
  
  previouslyFailingLinks.forEach((link, index) => {
    console.group(`Security Test ${index + 1}: ${link.substring(0, 50)}...`);
    
    const platform = detectVideoPlatform(link);
    const secureAttrs = getSecureIframeAttributes(link);
    
    console.log(`Platform: ${platform}`);
    console.log(`Referrer Policy: ${secureAttrs.referrerPolicy}`);
    console.log(`Allow Permissions: ${secureAttrs.allow}`);
    
    // Verify essential permissions are present
    const requiredPermissions = ['autoplay', 'fullscreen', 'encrypted-media'];
    const hasAllPermissions = requiredPermissions.every(perm => 
      secureAttrs.allow.includes(perm)
    );
    
    if (!hasAllPermissions) {
      console.error(`❌ Missing required permissions for video playback`);
      allPassed = false;
    } else {
      console.log(`✅ All required permissions present`);
    }
    
    // Verify referrer policy is appropriate
    const validReferrerPolicies = [
      'no-referrer',
      'strict-origin-when-cross-origin',
      'no-referrer-when-downgrade'
    ];
    
    if (!validReferrerPolicies.includes(secureAttrs.referrerPolicy)) {
      console.error(`❌ Invalid referrer policy: ${secureAttrs.referrerPolicy}`);
      allPassed = false;
    } else {
      console.log(`✅ Valid referrer policy: ${secureAttrs.referrerPolicy}`);
    }
    
    console.groupEnd();
  });
  
  const result = allPassed ? '✅ PASSED' : '❌ FAILED';
  console.log(`\n🔒 Enhanced Security Test: ${result}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Test iframe source validation
 */
export function testIframeValidation(): boolean {
  console.group('🛡️ Testing Iframe Source Validation');
  
  const validSources = [
    'https://www.youtube.com/embed/test',
    'https://player.vimeo.com/video/123',
    'https://streamtape.com/e/test',
    'https://filemoon.to/e/test'
  ];
  
  const invalidSources = [
    'http://insecure.com/embed/test', // HTTP not allowed
    'https://malicious.com/embed/test', // Not in allowed domains
    'javascript:alert("xss")', // Invalid protocol
    'data:text/html,<script>alert("xss")</script>' // Data URLs not allowed
  ];
  
  let allPassed = true;
  
  console.log('Testing valid sources:');
  validSources.forEach((src, index) => {
    const isValid = validateIframeSrc(src);
    if (!isValid) {
      console.error(`❌ Valid source rejected: ${src}`);
      allPassed = false;
    } else {
      console.log(`✅ ${index + 1}. ${src}`);
    }
  });
  
  console.log('\nTesting invalid sources:');
  invalidSources.forEach((src, index) => {
    const isValid = validateIframeSrc(src);
    if (isValid) {
      console.error(`❌ Invalid source accepted: ${src}`);
      allPassed = false;
    } else {
      console.log(`✅ ${index + 1}. Correctly rejected: ${src.substring(0, 50)}...`);
    }
  });
  
  const result = allPassed ? '✅ PASSED' : '❌ FAILED';
  console.log(`\n🛡️ Iframe Validation Test: ${result}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Test that previously failing platforms now work
 */
export function testPlatformCompatibility(): boolean {
  console.group('🎬 Testing Platform Compatibility');
  
  const platformTests = [
    {
      name: 'GradeHGPlus',
      url: 'https://gradehgplus.com/e/xvay1ggua7s7',
      expectedIssue: 'Sandboxed embed is not allowed',
      shouldWorkNow: true
    },
    {
      name: 'StreamTape',
      url: 'https://streamtape.com/v/YeRw6amy3MsvWa7/test.mkv',
      expectedIssue: 'took too long to respond',
      shouldWorkNow: true
    },
    {
      name: 'FileMoon',
      url: 'https://filemoon.to/e/ezfjmgsjwwsh',
      expectedIssue: 'took too long to respond',
      shouldWorkNow: true
    }
  ];
  
  let allPassed = true;
  
  platformTests.forEach((test, index) => {
    console.group(`${index + 1}. ${test.name} Compatibility`);
    
    const platform = detectVideoPlatform(test.url);
    const config = getIFrameConfig(test.url);
    const secureAttrs = getSecureIframeAttributes(test.url);
    
    console.log(`Platform detected: ${platform}`);
    console.log(`Previous issue: ${test.expectedIssue}`);
    console.log(`Sandbox restrictions: ${config.sandbox || 'none'}`);
    console.log(`Security validation: ${validateIframeSrc(test.url) ? 'passed' : 'failed'}`);
    
    // Check if sandbox restrictions have been removed
    const hasSandbox = config.sandbox && config.sandbox.trim() !== '';
    if (hasSandbox) {
      console.error(`❌ Still has sandbox restrictions that may cause "${test.expectedIssue}"`);
      allPassed = false;
    } else {
      console.log(`✅ No sandbox restrictions - should resolve "${test.expectedIssue}"`);
    }
    
    // Check referrer policy is appropriate for platform
    const referrerPolicy = secureAttrs.referrerPolicy;
    if (platform === 'filemoon' && referrerPolicy !== 'no-referrer') {
      console.warn(`⚠️ FileMoon may work better with no-referrer policy`);
    }
    
    console.groupEnd();
  });
  
  const result = allPassed ? '✅ PASSED' : '❌ FAILED';
  console.log(`\n🎬 Platform Compatibility Test: ${result}`);
  console.groupEnd();
  
  return allPassed;
}

/**
 * Run all sandbox removal and security tests
 */
export function runAllSandboxRemovalTests(): boolean {
  console.group('🧪 Running All Sandbox Removal & Security Tests');
  
  const results = [
    testSandboxRemoval(),
    testEnhancedSecurity(),
    testIframeValidation(),
    testPlatformCompatibility()
  ];
  
  const allPassed = results.every(result => result);
  const passedCount = results.filter(result => result).length;
  
  console.log(`\n📊 Test Results: ${passedCount}/${results.length} passed`);
  
  if (allPassed) {
    console.log('🎉 All tests passed! Sandbox restrictions have been successfully removed.');
    console.log('🔒 Enhanced security measures are in place.');
    console.log('🎬 Video playback should now work across all platforms.');
  } else {
    console.log('⚠️ Some tests failed. Check the details above.');
  }
  
  console.groupEnd();
  
  return allPassed;
}
