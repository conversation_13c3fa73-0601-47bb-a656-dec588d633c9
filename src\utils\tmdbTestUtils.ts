/**
 * TMDB Integration Testing Utilities
 * 
 * Comprehensive testing suite for TMDB API integration
 * Tests API connectivity, data fetching, and error handling
 */

import { 
  getComprehensiveContentData, 
  getMovieDetails, 
  getTVShowDetails,
  searchContent,
  isValidTMDBId,
  buildImageUrl,
  TMDBError
} from '@/services/tmdbService';

// Test data - known TMDB IDs for testing
export const TEST_TMDB_IDS = {
  movies: {
    inception: '27205',
    avengers: '299536',
    interstellar: '157336',
    matrix: '603'
  },
  tvShows: {
    breakingBad: '1396',
    strangerThings: '66732',
    mandalorian: '82856',
    gameOfThrones: '1399'
  },
  invalid: {
    nonExistent: '999999999',
    invalidFormat: 'abc123',
    empty: ''
  }
};

// Test results interface
interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration: number;
  data?: any;
  error?: any;
}

interface TestSuite {
  name: string;
  results: TestResult[];
  passed: number;
  failed: number;
  duration: number;
}

// Helper function to run individual tests
async function runTest(
  name: string, 
  testFunction: () => Promise<any>
): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const result = await testFunction();
    const duration = Date.now() - startTime;
    
    return {
      name,
      passed: true,
      message: 'Test passed successfully',
      duration,
      data: result
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    
    return {
      name,
      passed: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      duration,
      error
    };
  }
}

// Test TMDB ID validation
export async function testTMDBIdValidation(): Promise<TestSuite> {
  const tests = [
    () => {
      const result = isValidTMDBId(TEST_TMDB_IDS.movies.inception);
      if (!result) throw new Error('Valid TMDB ID rejected');
      return result;
    },
    () => {
      const result = isValidTMDBId(TEST_TMDB_IDS.invalid.invalidFormat);
      if (result) throw new Error('Invalid TMDB ID accepted');
      return result;
    },
    () => {
      const result = isValidTMDBId(TEST_TMDB_IDS.invalid.empty);
      if (result) throw new Error('Empty TMDB ID accepted');
      return result;
    }
  ];

  const startTime = Date.now();
  const results: TestResult[] = [];

  for (let i = 0; i < tests.length; i++) {
    const result = await runTest(`TMDB ID Validation Test ${i + 1}`, tests[i]);
    results.push(result);
  }

  const duration = Date.now() - startTime;
  const passed = results.filter(r => r.passed).length;
  const failed = results.length - passed;

  return {
    name: 'TMDB ID Validation',
    results,
    passed,
    failed,
    duration
  };
}

// Test movie data fetching
export async function testMovieDataFetching(): Promise<TestSuite> {
  const tests = [
    () => getMovieDetails(TEST_TMDB_IDS.movies.inception),
    () => getMovieDetails(TEST_TMDB_IDS.movies.avengers),
    () => getComprehensiveContentData(TEST_TMDB_IDS.movies.interstellar, 'movie')
  ];

  const startTime = Date.now();
  const results: TestResult[] = [];

  for (let i = 0; i < tests.length; i++) {
    const result = await runTest(`Movie Data Fetch Test ${i + 1}`, tests[i]);
    results.push(result);
  }

  const duration = Date.now() - startTime;
  const passed = results.filter(r => r.passed).length;
  const failed = results.length - passed;

  return {
    name: 'Movie Data Fetching',
    results,
    passed,
    failed,
    duration
  };
}

// Test TV show data fetching
export async function testTVShowDataFetching(): Promise<TestSuite> {
  const tests = [
    () => getTVShowDetails(TEST_TMDB_IDS.tvShows.breakingBad),
    () => getTVShowDetails(TEST_TMDB_IDS.tvShows.strangerThings),
    () => getComprehensiveContentData(TEST_TMDB_IDS.tvShows.mandalorian, 'tv')
  ];

  const startTime = Date.now();
  const results: TestResult[] = [];

  for (let i = 0; i < tests.length; i++) {
    const result = await runTest(`TV Show Data Fetch Test ${i + 1}`, tests[i]);
    results.push(result);
  }

  const duration = Date.now() - startTime;
  const passed = results.filter(r => r.passed).length;
  const failed = results.length - passed;

  return {
    name: 'TV Show Data Fetching',
    results,
    passed,
    failed,
    duration
  };
}

// Test error handling
export async function testErrorHandling(): Promise<TestSuite> {
  const tests = [
    async () => {
      try {
        await getMovieDetails(TEST_TMDB_IDS.invalid.nonExistent);
        throw new Error('Should have thrown an error for non-existent ID');
      } catch (error) {
        if (error instanceof TMDBError && error.statusCode === 404) {
          return 'Correctly handled 404 error';
        }
        throw error;
      }
    },
    async () => {
      try {
        await getComprehensiveContentData('');
        throw new Error('Should have thrown an error for empty ID');
      } catch (error) {
        return 'Correctly handled empty ID error';
      }
    }
  ];

  const startTime = Date.now();
  const results: TestResult[] = [];

  for (let i = 0; i < tests.length; i++) {
    const result = await runTest(`Error Handling Test ${i + 1}`, tests[i]);
    results.push(result);
  }

  const duration = Date.now() - startTime;
  const passed = results.filter(r => r.passed).length;
  const failed = results.length - passed;

  return {
    name: 'Error Handling',
    results,
    passed,
    failed,
    duration
  };
}

// Test image URL building
export async function testImageUrlBuilding(): Promise<TestSuite> {
  const tests = [
    () => {
      const url = buildImageUrl('/test.jpg', 'poster', 'large');
      if (!url || !url.includes('test.jpg')) {
        throw new Error('Failed to build poster URL');
      }
      return url;
    },
    () => {
      const url = buildImageUrl('/backdrop.jpg', 'backdrop', 'original');
      if (!url || !url.includes('backdrop.jpg')) {
        throw new Error('Failed to build backdrop URL');
      }
      return url;
    },
    () => {
      const url = buildImageUrl(null, 'poster', 'medium');
      if (url !== null) {
        throw new Error('Should return null for null path');
      }
      return 'Correctly handled null path';
    }
  ];

  const startTime = Date.now();
  const results: TestResult[] = [];

  for (let i = 0; i < tests.length; i++) {
    const result = await runTest(`Image URL Building Test ${i + 1}`, tests[i]);
    results.push(result);
  }

  const duration = Date.now() - startTime;
  const passed = results.filter(r => r.passed).length;
  const failed = results.length - passed;

  return {
    name: 'Image URL Building',
    results,
    passed,
    failed,
    duration
  };
}

// Test search functionality
export async function testSearchFunctionality(): Promise<TestSuite> {
  const tests = [
    () => searchContent('Inception', 'movie'),
    () => searchContent('Breaking Bad', 'tv'),
    () => searchContent('Marvel')
  ];

  const startTime = Date.now();
  const results: TestResult[] = [];

  for (let i = 0; i < tests.length; i++) {
    const result = await runTest(`Search Test ${i + 1}`, tests[i]);
    results.push(result);
  }

  const duration = Date.now() - startTime;
  const passed = results.filter(r => r.passed).length;
  const failed = results.length - passed;

  return {
    name: 'Search Functionality',
    results,
    passed,
    failed,
    duration
  };
}

// Run comprehensive TMDB integration tests
export async function runComprehensiveTMDBTests(): Promise<{
  suites: TestSuite[];
  summary: {
    totalTests: number;
    totalPassed: number;
    totalFailed: number;
    totalDuration: number;
    successRate: number;
  };
}> {
  console.group('🎬 Running Comprehensive TMDB Integration Tests');
  
  const startTime = Date.now();
  const suites: TestSuite[] = [];

  // Run all test suites
  suites.push(await testTMDBIdValidation());
  suites.push(await testMovieDataFetching());
  suites.push(await testTVShowDataFetching());
  suites.push(await testErrorHandling());
  suites.push(await testImageUrlBuilding());
  suites.push(await testSearchFunctionality());

  const totalDuration = Date.now() - startTime;
  
  // Calculate summary
  const totalTests = suites.reduce((sum, suite) => sum + suite.results.length, 0);
  const totalPassed = suites.reduce((sum, suite) => sum + suite.passed, 0);
  const totalFailed = suites.reduce((sum, suite) => sum + suite.failed, 0);
  const successRate = totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0;

  // Log results
  suites.forEach(suite => {
    console.group(`📋 ${suite.name} (${suite.passed}/${suite.results.length} passed)`);
    suite.results.forEach(result => {
      const icon = result.passed ? '✅' : '❌';
      console.log(`${icon} ${result.name} (${result.duration}ms)`);
      if (!result.passed) {
        console.error(`   Error: ${result.message}`);
      }
    });
    console.groupEnd();
  });

  console.log(`\n📊 Test Summary:`);
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${totalPassed}`);
  console.log(`   Failed: ${totalFailed}`);
  console.log(`   Success Rate: ${successRate}%`);
  console.log(`   Total Duration: ${totalDuration}ms`);

  if (successRate === 100) {
    console.log('🎉 All TMDB integration tests passed!');
  } else if (successRate >= 80) {
    console.log('✅ Most TMDB tests passed. Minor issues may exist.');
  } else {
    console.log('⚠️ Some TMDB tests failed. Please review the implementation.');
  }

  console.groupEnd();

  return {
    suites,
    summary: {
      totalTests,
      totalPassed,
      totalFailed,
      totalDuration,
      successRate
    }
  };
}
