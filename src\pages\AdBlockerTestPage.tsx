import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Shield,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Loader2,
  Play,
  TestTube,
  Bug,
  Zap,
  Clock,
  Target
} from 'lucide-react';
import { 
  detectAdBlockers, 
  quickAdBlockerCheck, 
  AdBlockerDetectionResult 
} from '@/utils/adBlockerDetection';
import SecureVideoPlayer from '@/components/SecureVideoPlayer';

const AdBlockerTestPage: React.FC = () => {
  const [isDetecting, setIsDetecting] = useState(false);
  const [detectionResult, setDetectionResult] = useState<AdBlockerDetectionResult | null>(null);
  const [quickCheckResult, setQuickCheckResult] = useState<boolean | null>(null);
  const [testStarted, setTestStarted] = useState(false);
  const [isRunningComprehensiveTest, setIsRunningComprehensiveTest] = useState(false);
  const [comprehensiveResults, setComprehensiveResults] = useState<any>(null);
  const [falsePositiveTest, setFalsePositiveTest] = useState<boolean | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);

  // Sample encoded video links for testing
  const testVideoLinks = "aHR0cHM6Ly9maWxlbW9vbi50by9lL3Rlc3QxfGh0dHBzOi8vc3RyZWFtdGFwZS5jb20vZS90ZXN0Mg==";

  const runDetectionTest = async () => {
    setIsDetecting(true);
    setTestStarted(true);

    try {
      // Run quick check first
      console.log('Running quick ad blocker check...');
      const quickResult = await quickAdBlockerCheck();
      setQuickCheckResult(quickResult);

      // Run full detection
      console.log('Running full ad blocker detection...');
      const fullResult = await detectAdBlockers();
      setDetectionResult(fullResult);

      console.log('Detection results:', {
        quickCheck: quickResult,
        fullDetection: fullResult
      });

    } catch (error) {
      console.error('Detection test failed:', error);
    } finally {
      setIsDetecting(false);
    }
  };

  const runComprehensiveTest = async () => {
    setIsRunningComprehensiveTest(true);
    setComprehensiveResults(null);

    try {
      const startTime = performance.now();

      // Test 1: False Positive Check (clean browser test)
      console.log('Running false positive test...');
      const falsePositiveResult = await testForFalsePositives();
      setFalsePositiveTest(falsePositiveResult);

      // Test 2: Multiple detection runs for consistency
      console.log('Running consistency test...');
      const consistencyResults = await testDetectionConsistency();

      // Test 3: Performance metrics
      console.log('Measuring performance...');
      const endTime = performance.now();
      const performanceData = {
        totalTime: endTime - startTime,
        quickCheckTime: 0,
        fullDetectionTime: 0
      };

      // Measure individual test times
      const quickStart = performance.now();
      await quickAdBlockerCheck();
      const quickEnd = performance.now();
      performanceData.quickCheckTime = quickEnd - quickStart;

      const fullStart = performance.now();
      await detectAdBlockers();
      const fullEnd = performance.now();
      performanceData.fullDetectionTime = fullEnd - fullStart;

      setPerformanceMetrics(performanceData);

      setComprehensiveResults({
        falsePositiveTest: falsePositiveResult,
        consistencyTest: consistencyResults,
        performance: performanceData,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Comprehensive test failed:', error);
    } finally {
      setIsRunningComprehensiveTest(false);
    }
  };

  const testForFalsePositives = async (): Promise<boolean> => {
    // This test checks if the detection system produces false positives
    // in a clean browser environment (no ad blockers)

    // Run detection multiple times to check for consistency
    const results = [];
    for (let i = 0; i < 3; i++) {
      const result = await detectAdBlockers();
      results.push(result.hasAdBlocker);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // If any detection returns true when no ad blocker should be present,
    // it's likely a false positive
    const hasInconsistentResults = results.some(r => r !== results[0]);
    const hasUnexpectedDetection = results.some(r => r === true);

    return !hasInconsistentResults && !hasUnexpectedDetection;
  };

  const testDetectionConsistency = async () => {
    const results = [];

    for (let i = 0; i < 5; i++) {
      const quickResult = await quickAdBlockerCheck();
      const fullResult = await detectAdBlockers();

      results.push({
        run: i + 1,
        quickCheck: quickResult,
        fullDetection: fullResult.hasAdBlocker,
        detectedCount: fullResult.detectedBlockers.length,
        confidence: fullResult.totalConfidence
      });

      await new Promise(resolve => setTimeout(resolve, 200));
    }

    return results;
  };

  const resetTest = () => {
    setDetectionResult(null);
    setQuickCheckResult(null);
    setTestStarted(false);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-primary/10">
              <Shield className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-foreground">
                Ad Blocker Detection Test
              </h1>
              <p className="text-muted-foreground">
                Test the comprehensive ad blocker detection system
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Detection Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={runDetectionTest}
                disabled={isDetecting}
                className="flex-1"
              >
                {isDetecting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Detecting...
                  </>
                ) : (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    Run Ad Blocker Detection
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={resetTest}
                disabled={isDetecting}
                className="flex-1"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reset Test
              </Button>
            </div>

            {/* Comprehensive Testing Controls */}
            <div className="border-t border-border pt-4">
              <h3 className="text-lg font-medium text-foreground mb-3 flex items-center gap-2">
                <TestTube className="w-5 h-5" />
                Comprehensive Testing Suite
              </h3>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={runComprehensiveTest}
                  disabled={isRunningComprehensiveTest || isDetecting}
                  variant="secondary"
                  className="flex-1"
                >
                  {isRunningComprehensiveTest ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Running Tests...
                    </>
                  ) : (
                    <>
                      <Bug className="w-4 h-4 mr-2" />
                      Run False Positive Test
                    </>
                  )}
                </Button>
              </div>
            </div>

            {!testStarted && (
              <Alert>
                <AlertTriangle className="w-4 h-4" />
                <AlertDescription>
                  <strong>Testing Instructions:</strong>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>• Install an ad blocker (uBlock Origin, AdBlock Plus, etc.) to test detection</li>
                    <li>• Try different ad blockers to test specific detection methods</li>
                    <li>• Test on different browsers (Chrome, Firefox, Safari, Brave)</li>
                    <li>• Test on mobile devices for responsive design</li>
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Quick Check Results */}
        {quickCheckResult !== null && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="w-5 h-5" />
                Quick Check Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                {quickCheckResult ? (
                  <>
                    <AlertTriangle className="w-5 h-5 text-orange-500" />
                    <span className="text-foreground">Ad blocker detected (quick check)</span>
                    <Badge variant="destructive">Blocked</Badge>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-foreground">No ad blocker detected (quick check)</span>
                    <Badge variant="secondary">Clean</Badge>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Full Detection Results */}
        {detectionResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Full Detection Results
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {detectionResult.hasAdBlocker ? (
                    <>
                      <AlertTriangle className="w-5 h-5 text-orange-500" />
                      <span className="text-foreground font-medium">Ad Blockers Detected</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <span className="text-foreground font-medium">No Ad Blockers Detected</span>
                    </>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    {Math.round(detectionResult.totalConfidence)}% confidence
                  </Badge>
                  <Badge variant="secondary">
                    {detectionResult.detectedBlockers.length} detected
                  </Badge>
                </div>
              </div>

              {detectionResult.detectedBlockers.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium text-foreground">Detected Ad Blockers:</h4>
                  {detectionResult.detectedBlockers.map((blocker, index) => (
                    <div
                      key={index}
                      className="border border-border rounded-lg p-3 bg-card/50"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-foreground">{blocker.name}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {blocker.type}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {blocker.confidence}% confidence
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Detection method: {blocker.detectionMethod}
                      </p>
                    </div>
                  ))}
                </div>
              )}

              <div className="text-xs text-muted-foreground">
                Detection completed at: {new Date(detectionResult.detectionTimestamp).toLocaleString()}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Comprehensive Test Results */}
        {comprehensiveResults && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TestTube className="w-5 h-5" />
                Comprehensive Test Results
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* False Positive Test */}
              <div>
                <h3 className="text-lg font-medium text-foreground mb-3 flex items-center gap-2">
                  <Bug className="w-5 h-5" />
                  False Positive Test
                </h3>
                <Alert className={falsePositiveTest ? "border-green-500/20 bg-green-500/5" : "border-red-500/20 bg-red-500/5"}>
                  {falsePositiveTest ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <AlertTriangle className="w-4 h-4 text-red-500" />
                  )}
                  <AlertDescription className={falsePositiveTest ? "text-green-700 dark:text-green-400" : "text-red-700 dark:text-red-400"}>
                    {falsePositiveTest
                      ? "✅ No false positives detected - System correctly identifies clean browser environments"
                      : "❌ False positives detected - System may incorrectly flag clean browsers as having ad blockers"
                    }
                  </AlertDescription>
                </Alert>
              </div>

              {/* Performance Metrics */}
              {performanceMetrics && (
                <div>
                  <h3 className="text-lg font-medium text-foreground mb-3 flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Performance Metrics
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-muted/50 p-4 rounded-lg">
                      <div className="text-sm text-muted-foreground">Quick Check Time</div>
                      <div className="text-2xl font-bold text-foreground">
                        {performanceMetrics.quickCheckTime.toFixed(1)}ms
                      </div>
                    </div>
                    <div className="bg-muted/50 p-4 rounded-lg">
                      <div className="text-sm text-muted-foreground">Full Detection Time</div>
                      <div className="text-2xl font-bold text-foreground">
                        {performanceMetrics.fullDetectionTime.toFixed(1)}ms
                      </div>
                    </div>
                    <div className="bg-muted/50 p-4 rounded-lg">
                      <div className="text-sm text-muted-foreground">Total Test Time</div>
                      <div className="text-2xl font-bold text-foreground">
                        {performanceMetrics.totalTime.toFixed(1)}ms
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Consistency Test */}
              {comprehensiveResults.consistencyTest && (
                <div>
                  <h3 className="text-lg font-medium text-foreground mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Detection Consistency Test
                  </h3>
                  <div className="space-y-2">
                    {comprehensiveResults.consistencyTest.map((result: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Badge variant="outline">Run {result.run}</Badge>
                          <span className="text-sm">
                            Quick: {result.quickCheck ? '✅' : '❌'} |
                            Full: {result.fullDetection ? '✅' : '❌'} |
                            Count: {result.detectedCount}
                          </span>
                        </div>
                        <Badge variant={result.confidence > 70 ? "destructive" : "secondary"}>
                          {result.confidence.toFixed(1)}% confidence
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="text-xs text-muted-foreground">
                Test completed at: {new Date(comprehensiveResults.timestamp).toLocaleString()}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Video Player Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="w-5 h-5" />
              Video Player Integration Test
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              This video player will automatically detect ad blockers and show the modal if any are found.
            </p>
            <SecureVideoPlayer
              encodedVideoLinks={testVideoLinks}
              title="Ad Blocker Detection Test Video"
              showPlayerSelection={true}
              className="max-w-4xl"
            />
          </CardContent>
        </Card>

        {/* Mobile Responsiveness Test */}
        <Card>
          <CardHeader>
            <CardTitle>Mobile Responsiveness Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertTriangle className="w-4 h-4" />
              <AlertDescription>
                <strong>Mobile Testing Checklist:</strong>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Test on screen widths: 320px, 375px, 414px, 768px, 1024px</li>
                  <li>• Verify modal is readable and buttons are touch-friendly</li>
                  <li>• Check that instructions are clearly visible</li>
                  <li>• Ensure video player blocking works on mobile</li>
                  <li>• Test both portrait and landscape orientations</li>
                </ul>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdBlockerTestPage;
