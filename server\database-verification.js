#!/usr/bin/env node

/**
 * StreamDB Online - Database Connection Verification
 * 
 * Comprehensive database testing:
 * 1. Connection verification
 * 2. Table structure validation
 * 3. CRUD operations testing
 * 4. Data integrity checks
 * 5. Performance testing
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class DatabaseVerifier {
  constructor() {
    this.connection = null;
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  addTest(category, name, status, message, details = null) {
    this.tests.push({ category, name, status, message, details });
    
    if (status === 'pass') {
      this.passed++;
      log(`✅ [${category}] ${name}: ${message}`, 'green');
    } else if (status === 'fail') {
      this.failed++;
      log(`❌ [${category}] ${name}: ${message}`, 'red');
      if (details) log(`   Details: ${details}`, 'yellow');
    } else if (status === 'warning') {
      log(`⚠️  [${category}] ${name}: ${message}`, 'yellow');
      if (details) log(`   Details: ${details}`, 'yellow');
    }
  }

  async createConnection() {
    try {
      log('\n🔌 Establishing Database Connection...', 'cyan');
      
      const config = {
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        charset: 'utf8mb4',
        timezone: '+00:00'
      };

      // Use socket if specified (more secure for local connections)
      if (process.env.DB_SOCKET) {
        config.socketPath = process.env.DB_SOCKET;
        delete config.host;
        delete config.port;
        log(`Using socket connection: ${process.env.DB_SOCKET}`, 'blue');
      } else if (process.env.DB_PORT) {
        config.port = parseInt(process.env.DB_PORT);
        log(`Using TCP connection: ${config.host}:${config.port}`, 'blue');
      }

      this.connection = await mysql.createConnection(config);
      
      this.addTest('Connection', 'Database Connection', 'pass', 
        'Successfully connected to database');
      
      return true;
    } catch (error) {
      this.addTest('Connection', 'Database Connection', 'fail', 
        'Failed to connect to database', error.message);
      return false;
    }
  }

  async testBasicQueries() {
    log('\n📊 Testing Basic Database Queries...', 'cyan');
    
    try {
      // Test basic SELECT
      const [rows] = await this.connection.execute('SELECT 1 as test');
      if (rows[0].test === 1) {
        this.addTest('Queries', 'Basic SELECT', 'pass', 'Basic queries working');
      } else {
        this.addTest('Queries', 'Basic SELECT', 'fail', 'Basic query returned unexpected result');
      }

      // Test database info
      const [dbInfo] = await this.connection.execute('SELECT DATABASE() as current_db, VERSION() as version');
      this.addTest('Queries', 'Database Info', 'pass', 
        `Connected to: ${dbInfo[0].current_db} (MySQL ${dbInfo[0].version})`);

    } catch (error) {
      this.addTest('Queries', 'Basic Queries', 'fail', 
        'Basic database queries failed', error.message);
    }
  }

  async verifyTableStructure() {
    log('\n🏗️ Verifying Table Structure...', 'cyan');
    
    const requiredTables = [
      'admin_users',
      'content', 
      'categories',
      'genres',
      'languages',
      'quality_options',
      'audio_tracks',
      'seasons',
      'episodes',
      'admin_sessions',
      'admin_security_logs'
    ];

    try {
      // Get all tables
      const [tables] = await this.connection.execute('SHOW TABLES');
      const tableNames = tables.map(row => Object.values(row)[0]);
      
      for (const table of requiredTables) {
        if (tableNames.includes(table)) {
          this.addTest('Schema', `Table: ${table}`, 'pass', 'Table exists');
          
          // Check table structure
          const [columns] = await this.connection.execute(`DESCRIBE ${table}`);
          this.addTest('Schema', `${table} Structure`, 'pass', 
            `Table has ${columns.length} columns`);
        } else {
          this.addTest('Schema', `Table: ${table}`, 'fail', 
            'Required table missing');
        }
      }

      // Check for extra tables (might indicate data)
      const extraTables = tableNames.filter(name => !requiredTables.includes(name));
      if (extraTables.length > 0) {
        this.addTest('Schema', 'Additional Tables', 'pass', 
          `Found additional tables: ${extraTables.join(', ')}`);
      }

    } catch (error) {
      this.addTest('Schema', 'Table Verification', 'fail', 
        'Failed to verify table structure', error.message);
    }
  }

  async testCRUDOperations() {
    log('\n🔄 Testing CRUD Operations...', 'cyan');
    
    try {
      // Test Categories CRUD
      await this.testCategoryCRUD();
      
      // Test Content CRUD
      await this.testContentCRUD();
      
      // Test Admin Users (read only for security)
      await this.testAdminUsersRead();
      
    } catch (error) {
      this.addTest('CRUD', 'CRUD Operations', 'fail', 
        'CRUD testing failed', error.message);
    }
  }

  async testCategoryCRUD() {
    try {
      // CREATE - Insert test category
      const testCategoryId = Date.now();
      await this.connection.execute(
        'INSERT INTO categories (name, type, slug, description, is_active) VALUES (?, ?, ?, ?, ?)',
        [`Test Category ${testCategoryId}`, 'both', `test-${testCategoryId}`, 'Test category for verification', 1]
      );
      this.addTest('CRUD', 'Category CREATE', 'pass', 'Successfully created test category');

      // READ - Select the category
      const [categories] = await this.connection.execute(
        'SELECT * FROM categories WHERE slug = ?', [`test-${testCategoryId}`]
      );
      if (categories.length > 0) {
        this.addTest('CRUD', 'Category READ', 'pass', 'Successfully read category data');
      } else {
        this.addTest('CRUD', 'Category READ', 'fail', 'Could not read created category');
      }

      // UPDATE - Modify the category
      await this.connection.execute(
        'UPDATE categories SET description = ? WHERE slug = ?',
        ['Updated test description', `test-${testCategoryId}`]
      );
      this.addTest('CRUD', 'Category UPDATE', 'pass', 'Successfully updated category');

      // DELETE - Remove test category
      await this.connection.execute(
        'DELETE FROM categories WHERE slug = ?', [`test-${testCategoryId}`]
      );
      this.addTest('CRUD', 'Category DELETE', 'pass', 'Successfully deleted test category');

    } catch (error) {
      this.addTest('CRUD', 'Category CRUD', 'fail', 
        'Category CRUD operations failed', error.message);
    }
  }

  async testContentCRUD() {
    try {
      // Test if we can read content
      const [content] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM content LIMIT 1'
      );
      this.addTest('CRUD', 'Content READ', 'pass', 
        `Content table accessible (${content[0].count} items)`);

      // Test content with relations
      const [contentWithCategories] = await this.connection.execute(`
        SELECT c.*, cat.name as category_name 
        FROM content c 
        LEFT JOIN categories cat ON c.category_id = cat.id 
        LIMIT 5
      `);
      this.addTest('CRUD', 'Content Relations', 'pass', 
        `Content-category relations working (${contentWithCategories.length} items tested)`);

    } catch (error) {
      this.addTest('CRUD', 'Content Operations', 'fail', 
        'Content operations failed', error.message);
    }
  }

  async testAdminUsersRead() {
    try {
      const [users] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM admin_users WHERE is_active = 1'
      );
      this.addTest('CRUD', 'Admin Users', 'pass', 
        `Admin users table accessible (${users[0].count} active users)`);

      // Check if there's at least one admin user
      if (users[0].count > 0) {
        this.addTest('Security', 'Admin User Exists', 'pass', 
          'At least one admin user configured');
      } else {
        this.addTest('Security', 'Admin User Exists', 'warning', 
          'No active admin users found - you may need to create one');
      }

    } catch (error) {
      this.addTest('CRUD', 'Admin Users Read', 'fail', 
        'Could not access admin users table', error.message);
    }
  }

  async testDataIntegrity() {
    log('\n🔍 Testing Data Integrity...', 'cyan');
    
    try {
      // Check for orphaned content (content without valid categories)
      const [orphanedContent] = await this.connection.execute(`
        SELECT COUNT(*) as count 
        FROM content c 
        LEFT JOIN categories cat ON c.category_id = cat.id 
        WHERE c.category_id IS NOT NULL AND cat.id IS NULL
      `);
      
      if (orphanedContent[0].count === 0) {
        this.addTest('Integrity', 'Content-Category Relations', 'pass', 
          'No orphaned content found');
      } else {
        this.addTest('Integrity', 'Content-Category Relations', 'warning', 
          `Found ${orphanedContent[0].count} content items with invalid categories`);
      }

      // Check for duplicate content
      const [duplicates] = await this.connection.execute(`
        SELECT title, COUNT(*) as count 
        FROM content 
        GROUP BY title 
        HAVING COUNT(*) > 1 
        LIMIT 5
      `);
      
      if (duplicates.length === 0) {
        this.addTest('Integrity', 'Duplicate Content', 'pass', 
          'No duplicate content titles found');
      } else {
        this.addTest('Integrity', 'Duplicate Content', 'warning', 
          `Found ${duplicates.length} duplicate content titles`);
      }

    } catch (error) {
      this.addTest('Integrity', 'Data Integrity', 'fail', 
        'Data integrity checks failed', error.message);
    }
  }

  async testPerformance() {
    log('\n⚡ Testing Database Performance...', 'cyan');
    
    try {
      // Test query performance
      const startTime = Date.now();
      
      await this.connection.execute(`
        SELECT c.*, cat.name as category_name 
        FROM content c 
        LEFT JOIN categories cat ON c.category_id = cat.id 
        ORDER BY c.created_at DESC 
        LIMIT 20
      `);
      
      const queryTime = Date.now() - startTime;
      
      if (queryTime < 100) {
        this.addTest('Performance', 'Query Speed', 'pass', 
          `Fast query performance (${queryTime}ms)`);
      } else if (queryTime < 500) {
        this.addTest('Performance', 'Query Speed', 'warning', 
          `Moderate query performance (${queryTime}ms)`);
      } else {
        this.addTest('Performance', 'Query Speed', 'fail', 
          `Slow query performance (${queryTime}ms)`);
      }

    } catch (error) {
      this.addTest('Performance', 'Performance Test', 'fail', 
        'Performance testing failed', error.message);
    }
  }

  async testAPIIntegration() {
    log('\n🔗 Testing API Integration...', 'cyan');
    
    try {
      // Test if the database config matches what the API expects
      const dbConfig = require('./config/database');
      
      if (typeof dbConfig.executeQuery === 'function') {
        this.addTest('Integration', 'Database Module', 'pass', 
          'Database module properly configured');
        
        // Test the database module
        const testResult = await dbConfig.executeQuery('SELECT 1 as test');
        if (testResult && testResult[0] && testResult[0].test === 1) {
          this.addTest('Integration', 'Database Module Function', 'pass', 
            'Database module functions correctly');
        } else {
          this.addTest('Integration', 'Database Module Function', 'fail', 
            'Database module not functioning properly');
        }
      } else {
        this.addTest('Integration', 'Database Module', 'fail', 
          'Database module not properly configured');
      }

    } catch (error) {
      this.addTest('Integration', 'API Integration', 'fail', 
        'API integration test failed', error.message);
    }
  }

  generateReport() {
    log('\n📊 Database Verification Report', 'bright');
    log('================================', 'bright');
    
    const total = this.passed + this.failed;
    const score = total > 0 ? Math.round((this.passed / total) * 100) : 0;
    
    log(`\n📈 Summary:`, 'cyan');
    log(`✅ Passed: ${this.passed}`, 'green');
    log(`❌ Failed: ${this.failed}`, 'red');
    log(`🎯 Database Health Score: ${score}%`, score >= 90 ? 'green' : score >= 70 ? 'yellow' : 'red');
    
    // Show failures
    const failures = this.tests.filter(t => t.status === 'fail');
    if (failures.length > 0) {
      log(`\n❌ FAILURES (${failures.length}):`, 'red');
      failures.forEach(test => {
        log(`❌ [${test.category}] ${test.name}: ${test.message}`, 'red');
        if (test.details) log(`   ${test.details}`, 'yellow');
      });
    }
    
    // Show warnings
    const warnings = this.tests.filter(t => t.status === 'warning');
    if (warnings.length > 0) {
      log(`\n⚠️  WARNINGS (${warnings.length}):`, 'yellow');
      warnings.forEach(test => {
        log(`⚠️  [${test.category}] ${test.name}: ${test.message}`, 'yellow');
      });
    }
    
    // Final verdict
    if (failures.length === 0) {
      log('\n🎉 DATABASE VERIFICATION PASSED!', 'green');
      log('✅ Your database is properly connected and functional.', 'green');
      log('🚀 Ready for production use!', 'green');
      return true;
    } else {
      log('\n🚨 DATABASE ISSUES DETECTED!', 'red');
      log('❌ Please resolve the failures before proceeding.', 'red');
      return false;
    }
  }

  async runVerification() {
    log('🔍 StreamDB Online - Database Verification', 'bright');
    log('==========================================', 'bright');
    
    try {
      const connected = await this.createConnection();
      if (!connected) {
        return false;
      }

      await this.testBasicQueries();
      await this.verifyTableStructure();
      await this.testCRUDOperations();
      await this.testDataIntegrity();
      await this.testPerformance();
      await this.testAPIIntegration();
      
      return this.generateReport();
      
    } catch (error) {
      log(`\n❌ Database verification failed: ${error.message}`, 'red');
      return false;
    } finally {
      if (this.connection) {
        await this.connection.end();
        log('\n🔌 Database connection closed.', 'blue');
      }
    }
  }
}

// Run verification if called directly
if (require.main === module) {
  const verifier = new DatabaseVerifier();
  verifier.runVerification().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Database verification failed:', error);
    process.exit(1);
  });
}

module.exports = DatabaseVerifier;
