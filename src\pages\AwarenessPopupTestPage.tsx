import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw,
  Clock,
  Info,
  Heart,
  Database
} from 'lucide-react';
import AdBlockerAwarenessPopup from '@/components/AdBlockerAwarenessPopup';
import { 
  shouldShowAwarenessPopup,
  recordPopupShown,
  recordPopupDismissed,
  getTrackingStats,
  resetTrackingData,
  getNextShowTime,
  isFrequentDismisser,
  devUtils
} from '@/utils/adBlockerAwarenessTracking';

const AwarenessPopupTestPage: React.FC = () => {
  const [showTestPopup, setShowTestPopup] = useState(false);
  const [trackingStats, setTrackingStats] = useState(getTrackingStats());
  const [testResults, setTestResults] = useState<string[]>([]);

  // Refresh tracking stats
  const refreshStats = () => {
    setTrackingStats(getTrackingStats());
  };

  // Add test result
  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  // Test functions
  const testShouldShow = () => {
    const shouldShow = shouldShowAwarenessPopup();
    addTestResult(`Should show popup: ${shouldShow}`);
    refreshStats();
  };

  const testForceShow = () => {
    const shouldShow = devUtils.forceShow();
    addTestResult(`Force show test: ${shouldShow}`);
    setShowTestPopup(true);
    recordPopupShown();
    refreshStats();
  };

  const testCustomInterval = () => {
    // Test with 1 minute interval
    const shouldShow = devUtils.setTestInterval(1/60); // 1 minute
    addTestResult(`Custom interval test (1 min): ${shouldShow}`);
    refreshStats();
  };

  const testRecordShown = () => {
    recordPopupShown();
    addTestResult('Recorded popup as shown');
    refreshStats();
  };

  const testRecordDismissed = () => {
    recordPopupDismissed();
    addTestResult('Recorded popup as dismissed');
    refreshStats();
  };

  const testResetData = () => {
    resetTrackingData();
    addTestResult('Reset all tracking data');
    refreshStats();
  };

  const testSetOldTimestamp = () => {
    // Set timestamp to 25 hours ago
    const oldTimestamp = Date.now() - (25 * 60 * 60 * 1000);
    devUtils.setLastShown(oldTimestamp);
    addTestResult('Set last shown to 25 hours ago');
    refreshStats();
  };

  const handleTestPopupDismiss = () => {
    setShowTestPopup(false);
    recordPopupDismissed();
    addTestResult('Test popup dismissed');
    refreshStats();
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  useEffect(() => {
    refreshStats();
  }, []);

  const nextShowTime = getNextShowTime();
  const isFrequent = isFrequentDismisser();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-primary/10">
              <Heart className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-foreground">
                Ad Blocker Awareness Popup Test
              </h1>
              <p className="text-muted-foreground">
                Test and verify the awareness popup functionality
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
        {/* Current Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Current Tracking Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Has Record</p>
                <Badge variant={trackingStats.hasRecord ? "default" : "secondary"}>
                  {trackingStats.hasRecord ? "Yes" : "No"}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Last Shown</p>
                <p className="text-sm text-foreground">
                  {trackingStats.lastShown 
                    ? trackingStats.lastShown.toLocaleString()
                    : "Never"
                  }
                </p>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Dismiss Count</p>
                <Badge variant="outline">
                  {trackingStats.dismissCount}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Days Since Last</p>
                <Badge variant={trackingStats.daysSinceLastShown >= 1 ? "default" : "secondary"}>
                  {trackingStats.daysSinceLastShown >= 0 
                    ? `${trackingStats.daysSinceLastShown} days`
                    : "N/A"
                  }
                </Badge>
              </div>
            </div>

            <div className="border-t border-border pt-4 space-y-2">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Next Show Time:</span>
                <span className="text-sm text-foreground">
                  {nextShowTime ? nextShowTime.toLocaleString() : "Will show immediately"}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Frequent Dismisser:</span>
                <Badge variant={isFrequent ? "destructive" : "secondary"}>
                  {isFrequent ? "Yes (3+ dismissals)" : "No"}
                </Badge>
              </div>

              {trackingStats.sessionId && (
                <div className="flex items-center gap-2">
                  <Database className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">Session ID:</span>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    {trackingStats.sessionId}
                  </code>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              <Button onClick={testShouldShow} variant="outline" className="w-full">
                Test Should Show
              </Button>
              
              <Button onClick={testForceShow} className="w-full">
                Force Show Popup
              </Button>
              
              <Button onClick={testCustomInterval} variant="outline" className="w-full">
                Test 1min Interval
              </Button>
              
              <Button onClick={testSetOldTimestamp} variant="outline" className="w-full">
                Set Old Timestamp
              </Button>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              <Button onClick={testRecordShown} variant="secondary" className="w-full">
                Record Shown
              </Button>
              
              <Button onClick={testRecordDismissed} variant="secondary" className="w-full">
                Record Dismissed
              </Button>
              
              <Button onClick={refreshStats} variant="outline" className="w-full">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh Stats
              </Button>
              
              <Button onClick={testResetData} variant="destructive" className="w-full">
                Reset All Data
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Test Results
              </div>
              <Button onClick={clearTestResults} variant="outline" size="sm">
                Clear Results
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {testResults.length > 0 ? (
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono bg-muted p-2 rounded">
                    {result}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-sm">No test results yet. Run some tests above.</p>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <Info className="w-4 h-4" />
              <AlertDescription>
                <strong>How to test the awareness popup:</strong>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Use "Force Show Popup" to test the popup appearance and behavior</li>
                  <li>• Use "Set Old Timestamp" then "Test Should Show" to simulate 24-hour interval</li>
                  <li>• Test dismissal behavior and tracking with "Record Dismissed"</li>
                  <li>• Check mobile responsiveness by resizing browser window</li>
                  <li>• Verify auto-close functionality (popup closes after 17 seconds)</li>
                  <li>• Test on actual homepage at "/" to verify route-specific behavior</li>
                </ul>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Test Popup */}
      <AdBlockerAwarenessPopup
        isVisible={showTestPopup}
        onDismiss={handleTestPopupDismiss}
        autoCloseDelay={17000}
      />
    </div>
  );
};

export default AwarenessPopupTestPage;
