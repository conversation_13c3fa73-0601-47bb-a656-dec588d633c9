/**
 * Content Filtering Utilities
 * 
 * This module provides utilities for filtering content based on publishing status,
 * featured flags, and carousel settings to ensure proper content visibility.
 */

import { MediaItem } from '@/types/media';

/**
 * Filter content that should be visible to public users
 * @param content - Array of media items
 * @returns Published content only
 */
export function getPublishedContent(content: MediaItem[]): MediaItem[] {
  return content.filter(item => {
    // In production with real data, this would check item.isPublished
    // For now, we assume all content in mediaData is published
    return item.isPublished !== false;
  });
}

/**
 * Filter content marked as featured
 * @param content - Array of media items
 * @returns Featured content only
 */
export function getFeaturedContent(content: MediaItem[]): MediaItem[] {
  return content.filter(item => {
    return item.isFeatured === true && item.isPublished !== false;
  });
}

/**
 * Filter content marked for hero carousel
 * @param content - Array of media items
 * @returns Carousel content only
 */
export function getCarouselContent(content: MediaItem[]): MediaItem[] {
  return content.filter(item => {
    return item.addToCarousel === true && item.isPublished !== false;
  });
}

/**
 * Get content by type with publishing filter
 * @param content - Array of media items
 * @param type - Content type to filter by
 * @param publishedOnly - Whether to include only published content
 * @returns Filtered content
 */
export function getContentByType(
  content: MediaItem[], 
  type: string, 
  publishedOnly: boolean = true
): MediaItem[] {
  return content.filter(item => {
    const typeMatch = item.type === type;
    const publishedMatch = publishedOnly ? item.isPublished !== false : true;
    return typeMatch && publishedMatch;
  });
}

/**
 * Sort content by creation date (newest first)
 * @param content - Array of media items
 * @returns Sorted content
 */
export function sortByCreationDate(content: MediaItem[]): MediaItem[] {
  return [...content].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
}

/**
 * Get homepage content sections with proper filtering
 * @param allContent - All available content
 * @param itemsPerSection - Number of items per section (default: 20)
 * @returns Object with filtered content for each section
 */
export function getHomepageContent(allContent: MediaItem[], itemsPerSection: number = 20) {
  const publishedContent = getPublishedContent(allContent);
  
  return {
    movies: sortByCreationDate(getContentByType(publishedContent, 'movie')).slice(0, itemsPerSection),
    series: sortByCreationDate(getContentByType(publishedContent, 'series')).slice(0, itemsPerSection),
    requested: sortByCreationDate(getContentByType(publishedContent, 'requested')).slice(0, itemsPerSection),
    featured: sortByCreationDate(getFeaturedContent(publishedContent)).slice(0, itemsPerSection),
    carousel: getCarouselContent(publishedContent).slice(0, 5) // Limit carousel to 5 items
  };
}

/**
 * Get content by category with publishing filter
 * @param content - Array of media items
 * @param category - Category to filter by
 * @param publishedOnly - Whether to include only published content
 * @returns Filtered content
 */
export function getContentByCategory(
  content: MediaItem[],
  category: string,
  publishedOnly: boolean = true
): MediaItem[] {
  return content.filter(item => {
    const categoryMatch = item.category === category;
    const publishedMatch = publishedOnly ? item.isPublished !== false : true;
    return categoryMatch && publishedMatch;
  });
}

/**
 * Get all available categories from content
 * @param content - Array of media items
 * @param publishedOnly - Whether to include only published content
 * @returns Array of unique categories
 */
export function getAvailableCategories(
  content: MediaItem[],
  publishedOnly: boolean = true
): string[] {
  const filteredContent = publishedOnly ? getPublishedContent(content) : content;
  const categories = filteredContent
    .map(item => item.category)
    .filter((category): category is string => Boolean(category));

  return Array.from(new Set(categories)).sort();
}

/**
 * Assign default category based on content type and language
 * @param item - Media item to categorize
 * @returns Suggested category
 */
export function getDefaultCategory(item: MediaItem): string {
  const type = item.type === 'movie' ? 'Movies' : 'Web Series';
  const primaryLanguage = item.languages?.[0] || 'English';

  // Map common languages to categories
  const languageMap: { [key: string]: string } = {
    'Hindi': 'Hindi',
    'English': 'English',
    'Tamil': 'Tamil',
    'Telugu': 'Telugu',
    'Malayalam': 'Malayalam',
    'Korean': 'Korean',
    'Japanese': 'Japanese',
    'Anime': 'Anime'
  };

  const categoryPrefix = languageMap[primaryLanguage] || 'English';
  return `${categoryPrefix} ${type}`;
}

/**
 * Check if content should be visible based on admin settings
 * @param item - Media item to check
 * @param context - Context where content will be displayed
 * @returns Whether content should be visible
 */
export function shouldShowContent(
  item: MediaItem,
  context: 'public' | 'featured' | 'carousel' | 'admin'
): boolean {
  switch (context) {
    case 'public':
      return item.isPublished !== false;
    case 'featured':
      return item.isPublished !== false && item.isFeatured === true;
    case 'carousel':
      return item.isPublished !== false && item.addToCarousel === true;
    case 'admin':
      return true; // Admin can see all content
    default:
      return false;
  }
}

/**
 * Get content statistics for admin dashboard
 * @param content - Array of media items
 * @returns Statistics object
 */
export function getContentStats(content: MediaItem[]) {
  const total = content.length;
  const published = getPublishedContent(content).length;
  const featured = getFeaturedContent(content).length;
  const carousel = getCarouselContent(content).length;
  const drafts = total - published;
  
  const byType = {
    movies: getContentByType(content, 'movie', false).length,
    series: getContentByType(content, 'series', false).length,
    requested: getContentByType(content, 'requested', false).length
  };
  
  return {
    total,
    published,
    drafts,
    featured,
    carousel,
    byType,
    publishedPercentage: total > 0 ? Math.round((published / total) * 100) : 0
  };
}

/**
 * Validate content publishing settings
 * @param item - Media item to validate
 * @returns Validation result with warnings
 */
export function validateContentSettings(item: MediaItem) {
  const warnings: string[] = [];
  
  if (item.addToCarousel && !item.isPublished) {
    warnings.push('Content is marked for carousel but not published');
  }
  
  if (item.isFeatured && !item.isPublished) {
    warnings.push('Content is marked as featured but not published');
  }
  
  if (item.addToCarousel && !item.coverImage) {
    warnings.push('Carousel content should have a cover image');
  }
  
  if (item.isPublished && !item.description) {
    warnings.push('Published content should have a description');
  }
  
  return {
    isValid: warnings.length === 0,
    warnings
  };
}
