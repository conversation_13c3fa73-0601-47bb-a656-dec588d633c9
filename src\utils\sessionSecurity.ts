/**
 * Session Security Utilities
 * Advanced session management with security features
 */

import { SessionData, AuthEventLog } from '@/types/auth';
import { AUTH_CONFIG, SECURITY_CONFIG } from '@/config/auth';
import { SecurityLogger } from '@/utils/authUtils';

/**
 * Session fingerprinting for additional security
 */
export class SessionFingerprint {
  /**
   * Generate a browser fingerprint for session validation
   */
  static generate(): string {
    const components = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      screen.colorDepth,
      new Date().getTimezoneOffset(),
      navigator.platform,
      navigator.cookieEnabled ? '1' : '0',
    ];

    // Simple hash function for fingerprint
    let hash = 0;
    const combined = components.join('|');
    
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(16);
  }

  /**
   * Validate session fingerprint
   */
  static validate(storedFingerprint: string): boolean {
    const currentFingerprint = this.generate();
    return storedFingerprint === currentFingerprint;
  }
}

/**
 * Enhanced session storage with security features
 */
export class SecureSessionManager {
  private static readonly SESSION_KEY = 'streamdb_auth_session';
  private static readonly FINGERPRINT_KEY = 'streamdb_session_fingerprint';
  private static readonly ACTIVITY_KEY = 'streamdb_session_activity';

  /**
   * Create a new secure session
   */
  static createSession(sessionData: SessionData): boolean {
    try {
      // Generate session fingerprint
      const fingerprint = SessionFingerprint.generate();
      
      // Enhanced session data with security metadata
      const enhancedSessionData = {
        ...sessionData,
        fingerprint,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        ipAddress: this.getClientIP(),
        userAgent: navigator.userAgent,
      };

      // Store session data
      const encryptedData = this.encryptSessionData(enhancedSessionData);
      sessionStorage.setItem(this.SESSION_KEY, encryptedData);
      
      // Store fingerprint separately for validation
      sessionStorage.setItem(this.FINGERPRINT_KEY, fingerprint);
      
      // Initialize activity tracking
      this.updateActivity();

      SecurityLogger.logEvent('SESSION_CREATED', {
        userId: sessionData.user.id,
        fingerprint: fingerprint.substring(0, 8) + '...',
        expiresAt: sessionData.expiresAt,
      }, 'low');

      return true;
    } catch (error) {
      console.error('Failed to create session:', error);
      SecurityLogger.logEvent('SECURITY_VIOLATION', {
        reason: 'Session creation failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, 'high');
      return false;
    }
  }

  /**
   * Retrieve and validate session
   */
  static getSession(): SessionData | null {
    try {
      const encryptedData = sessionStorage.getItem(this.SESSION_KEY);
      const storedFingerprint = sessionStorage.getItem(this.FINGERPRINT_KEY);

      if (!encryptedData || !storedFingerprint) {
        return null;
      }

      // Validate fingerprint
      if (!SessionFingerprint.validate(storedFingerprint)) {
        SecurityLogger.logEvent('SECURITY_VIOLATION', {
          reason: 'Session fingerprint mismatch',
          storedFingerprint: storedFingerprint.substring(0, 8) + '...',
          currentFingerprint: SessionFingerprint.generate().substring(0, 8) + '...',
        }, 'critical');
        
        this.destroySession();
        return null;
      }

      // Decrypt and parse session data
      const sessionData = this.decryptSessionData(encryptedData);
      if (!sessionData) {
        return null;
      }

      // Check session expiry
      if (Date.now() > sessionData.expiresAt) {
        SecurityLogger.logEvent('SESSION_EXPIRED', {
          userId: sessionData.user?.id,
          expiredAt: sessionData.expiresAt,
        }, 'medium');
        
        this.destroySession();
        return null;
      }

      // Check for session inactivity
      if (this.isSessionInactive(sessionData)) {
        SecurityLogger.logEvent('SESSION_EXPIRED', {
          userId: sessionData.user?.id,
          reason: 'Inactivity timeout',
          lastActivity: sessionData.lastActivity,
        }, 'medium');
        
        this.destroySession();
        return null;
      }

      // Update activity timestamp
      this.updateActivity();

      return sessionData;
    } catch (error) {
      console.error('Failed to retrieve session:', error);
      this.destroySession();
      return null;
    }
  }

  /**
   * Update session activity timestamp
   */
  static updateActivity(): void {
    try {
      const now = Date.now();
      sessionStorage.setItem(this.ACTIVITY_KEY, now.toString());

      // Update session data with new activity timestamp
      const sessionData = this.getSessionWithoutValidation();
      if (sessionData) {
        sessionData.lastActivity = now;
        const encryptedData = this.encryptSessionData(sessionData);
        sessionStorage.setItem(this.SESSION_KEY, encryptedData);
      }
    } catch (error) {
      console.error('Failed to update activity:', error);
    }
  }

  /**
   * Destroy session and clear all related data
   */
  static destroySession(): void {
    try {
      // Get session data for logging before destruction
      const sessionData = this.getSessionWithoutValidation();
      
      // Clear all session-related storage
      sessionStorage.removeItem(this.SESSION_KEY);
      sessionStorage.removeItem(this.FINGERPRINT_KEY);
      sessionStorage.removeItem(this.ACTIVITY_KEY);

      SecurityLogger.logEvent('SESSION_DESTROYED', {
        userId: sessionData?.user?.id,
        reason: 'Manual logout or security violation',
      }, 'low');
    } catch (error) {
      console.error('Failed to destroy session:', error);
    }
  }

  /**
   * Check if session is inactive based on timeout
   */
  private static isSessionInactive(sessionData: any): boolean {
    if (!SECURITY_CONFIG.enableSessionTimeout) {
      return false;
    }

    const lastActivity = sessionData.lastActivity || sessionData.createdAt;
    const inactivityTimeout = 2 * 60 * 60 * 1000; // 2 hours of inactivity
    
    return (Date.now() - lastActivity) > inactivityTimeout;
  }

  /**
   * Get session data without validation (for internal use)
   */
  private static getSessionWithoutValidation(): any {
    try {
      const encryptedData = sessionStorage.getItem(this.SESSION_KEY);
      return encryptedData ? this.decryptSessionData(encryptedData) : null;
    } catch {
      return null;
    }
  }

  /**
   * Encrypt session data
   */
  private static encryptSessionData(data: any): string {
    try {
      const jsonString = JSON.stringify(data);
      // Simple XOR encryption (in production, use proper encryption)
      let encrypted = '';
      const key = 'StreamDB_Session_Key_2024';
      
      for (let i = 0; i < jsonString.length; i++) {
        encrypted += String.fromCharCode(
          jsonString.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }
      
      return btoa(encrypted);
    } catch (error) {
      throw new Error('Failed to encrypt session data');
    }
  }

  /**
   * Decrypt session data
   */
  private static decryptSessionData(encryptedData: string): any {
    try {
      const encrypted = atob(encryptedData);
      let decrypted = '';
      const key = 'StreamDB_Session_Key_2024';
      
      for (let i = 0; i < encrypted.length; i++) {
        decrypted += String.fromCharCode(
          encrypted.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }
      
      return JSON.parse(decrypted);
    } catch (error) {
      throw new Error('Failed to decrypt session data');
    }
  }

  /**
   * Get client IP address (best effort)
   */
  private static getClientIP(): string {
    // This is limited in browser environment
    // In production, get from server-side headers
    return 'client-side-unknown';
  }

  /**
   * Validate session integrity
   */
  static validateSessionIntegrity(): boolean {
    const sessionData = this.getSession();
    if (!sessionData) return false;

    // Check for tampering indicators
    const requiredFields = ['user', 'token', 'expiresAt', 'createdAt'];
    for (const field of requiredFields) {
      if (!(field in sessionData)) {
        SecurityLogger.logEvent('SECURITY_VIOLATION', {
          reason: 'Session data tampering detected',
          missingField: field,
        }, 'critical');
        
        this.destroySession();
        return false;
      }
    }

    return true;
  }

  /**
   * Get session statistics for monitoring
   */
  static getSessionStats(): {
    isActive: boolean;
    timeRemaining: number;
    lastActivity: number;
    fingerprint: string;
  } {
    const sessionData = this.getSessionWithoutValidation();
    const fingerprint = sessionStorage.getItem(this.FINGERPRINT_KEY) || '';
    
    if (!sessionData) {
      return {
        isActive: false,
        timeRemaining: 0,
        lastActivity: 0,
        fingerprint: '',
      };
    }

    return {
      isActive: Date.now() < sessionData.expiresAt,
      timeRemaining: Math.max(0, sessionData.expiresAt - Date.now()),
      lastActivity: sessionData.lastActivity || sessionData.createdAt,
      fingerprint: fingerprint.substring(0, 8) + '...',
    };
  }
}

/**
 * Session activity monitor
 */
export class SessionActivityMonitor {
  private static activityTimer: NodeJS.Timeout | null = null;
  private static isMonitoring = false;

  /**
   * Start monitoring user activity
   */
  static startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // Monitor user interactions
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const activityHandler = () => {
      SecureSessionManager.updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, activityHandler, { passive: true });
    });

    // Periodic session validation
    this.activityTimer = setInterval(() => {
      SecureSessionManager.validateSessionIntegrity();
    }, 60000); // Check every minute

    SecurityLogger.logEvent('SESSION_MONITORING_STARTED', {}, 'low');
  }

  /**
   * Stop monitoring user activity
   */
  static stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    if (this.activityTimer) {
      clearInterval(this.activityTimer);
      this.activityTimer = null;
    }

    SecurityLogger.logEvent('SESSION_MONITORING_STOPPED', {}, 'low');
  }
}

/**
 * Initialize session security when module is loaded
 */
if (typeof window !== 'undefined') {
  // Start activity monitoring when the module loads
  SessionActivityMonitor.startMonitoring();

  // Clean up on page unload
  window.addEventListener('beforeunload', () => {
    SessionActivityMonitor.stopMonitoring();
  });
}
